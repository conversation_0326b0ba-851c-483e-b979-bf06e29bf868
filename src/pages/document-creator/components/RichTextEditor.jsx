import React, { useState, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const RichTextEditor = ({ content, onContentChange, collaborators, suggestions }) => {
  const [selectedText, setSelectedText] = useState('');
  const [showFormatMenu, setShowFormatMenu] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeCollaborators] = useState([
    { id: 1, name: '<PERSON>', color: '#3B82F6', cursor: { x: 45, y: 120 } },
    { id: 2, name: '<PERSON>', color: '#10B981', cursor: { x: 200, y: 180 } }
  ]);
  
  const editorRef = useRef(null);

  const mockContent = content || `<h1>Introduction to Digital Marketing</h1>

<p>Digital marketing has revolutionized the way businesses connect with their customers. In today's interconnected world, traditional marketing methods are being supplemented and often replaced by digital strategies that offer unprecedented reach, targeting capabilities, and measurable results.</p>

<h2>What is Digital Marketing?</h2>

<p>Digital marketing encompasses all marketing efforts that use electronic devices or the internet. Businesses leverage digital channels such as search engines, social media, email, and other websites to connect with current and prospective customers.</p>

<p>The key components of digital marketing include:</p>

<ul>
<li><strong>Search Engine Optimization (SEO)</strong> - Optimizing content to rank higher in search results</li>
<li><strong>Content Marketing</strong> - Creating valuable content to attract and engage audiences</li>
<li><strong>Social Media Marketing</strong> - Using social platforms to promote products and services</li>
<li><strong>Email Marketing</strong> - Direct communication with customers through email campaigns</li>
<li><strong>Pay-Per-Click (PPC)</strong> - Paid advertising on search engines and social platforms</li>
</ul>

<h2>Evolution of Marketing</h2>

<p>The marketing landscape has undergone significant transformation over the past few decades. From traditional print advertisements and television commercials, we've moved to a digital-first approach that prioritizes data-driven decision making and personalized customer experiences.</p>

<blockquote>
<p>"The best marketing doesn't feel like marketing." - Tom Fishburne</p>
</blockquote>

<p>This evolution has been driven by several factors:</p>`;

  const aiSuggestions = suggestions || [
    {
      id: 1,
      type: 'grammar',
      text: 'Consider changing "has revolutionized" to "has transformed" for better flow.',
      position: { start: 45, end: 62 },
      suggestion: 'has transformed'
    },
    {
      id: 2,
      type: 'style',
      text: 'This sentence could be more concise. Consider breaking it into two sentences.',
      position: { start: 120, end: 280 },
      suggestion: 'Split sentence'
    },
    {
      id: 3,
      type: 'enhancement',
      text: 'Add a statistic here to strengthen your point.',
      position: { start: 300, end: 300 },
      suggestion: 'Insert statistic'
    }
  ];

  const formatButtons = [
    { name: 'Bold', icon: 'Bold', command: 'bold' },
    { name: 'Italic', icon: 'Italic', command: 'italic' },
    { name: 'Underline', icon: 'Underline', command: 'underline' },
    { name: 'Strikethrough', icon: 'Strikethrough', command: 'strikethrough' }
  ];

  const headingButtons = [
    { name: 'H1', command: 'formatBlock', value: 'h1' },
    { name: 'H2', command: 'formatBlock', value: 'h2' },
    { name: 'H3', command: 'formatBlock', value: 'h3' },
    { name: 'P', command: 'formatBlock', value: 'p' }
  ];

  const handleFormat = (command, value = null) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
  };

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection.toString().length > 0) {
      setSelectedText(selection.toString());
      setShowFormatMenu(true);
    } else {
      setShowFormatMenu(false);
    }
  };

  const handleInsertCitation = () => {
    const citation = '[1] Smith, J. (2023). Digital Marketing Fundamentals. Marketing Press.';
    document.execCommand('insertText', false, citation);
  };

  const handleAcceptSuggestion = (suggestion) => {
    console.log('Accepting suggestion:', suggestion);
    // Implementation would apply the suggestion to the content
  };

  const handleRejectSuggestion = (suggestion) => {
    console.log('Rejecting suggestion:', suggestion);
    // Implementation would dismiss the suggestion
  };

  return (
    <div className="h-full flex flex-col bg-surface">
      {/* Toolbar */}
      <div className="border-b border-border p-3">
        <div className="flex items-center space-x-1 flex-wrap gap-2">
          {/* Formatting Buttons */}
          <div className="flex items-center space-x-1 border-r border-border pr-3">
            {formatButtons.map((btn) => (
              <Button
                key={btn.command}
                variant="ghost"
                onClick={() => handleFormat(btn.command)}
                className="w-8 h-8 p-0"
                title={btn.name}
              >
                <Icon name={btn.icon} size={16} />
              </Button>
            ))}
          </div>

          {/* Heading Buttons */}
          <div className="flex items-center space-x-1 border-r border-border pr-3">
            {headingButtons.map((btn) => (
              <Button
                key={btn.value}
                variant="ghost"
                onClick={() => handleFormat(btn.command, btn.value)}
                className="px-2 py-1 text-sm font-medium"
              >
                {btn.name}
              </Button>
            ))}
          </div>

          {/* List Buttons */}
          <div className="flex items-center space-x-1 border-r border-border pr-3">
            <Button
              variant="ghost"
              onClick={() => handleFormat('insertUnorderedList')}
              className="w-8 h-8 p-0"
              title="Bullet List"
            >
              <Icon name="List" size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => handleFormat('insertOrderedList')}
              className="w-8 h-8 p-0"
              title="Numbered List"
            >
              <Icon name="ListOrdered" size={16} />
            </Button>
          </div>

          {/* Insert Buttons */}
          <div className="flex items-center space-x-1 border-r border-border pr-3">
            <Button
              variant="ghost"
              onClick={() => handleFormat('createLink', prompt('Enter URL:'))}
              className="w-8 h-8 p-0"
              title="Insert Link"
            >
              <Icon name="Link" size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={handleInsertCitation}
              className="w-8 h-8 p-0"
              title="Insert Citation"
            >
              <Icon name="Quote" size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => console.log('Insert image')}
              className="w-8 h-8 p-0"
              title="Insert Image"
            >
              <Icon name="Image" size={16} />
            </Button>
          </div>

          {/* AI Tools */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              onClick={() => setShowSuggestions(!showSuggestions)}
              className={`w-8 h-8 p-0 ${showSuggestions ? 'bg-accent/10 text-accent' : ''}`}
              title="AI Suggestions"
            >
              <Icon name="Sparkles" size={16} />
            </Button>
            <Button
              variant="ghost"
              onClick={() => console.log('Grammar check')}
              className="w-8 h-8 p-0"
              title="Grammar Check"
            >
              <Icon name="CheckCircle" size={16} />
            </Button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="flex-1 relative">
        {/* Collaborator Cursors */}
        {activeCollaborators.map((collaborator) => (
          <div
            key={collaborator.id}
            className="absolute z-10 pointer-events-none"
            style={{ 
              left: collaborator.cursor.x, 
              top: collaborator.cursor.y,
              transform: 'translate(-50%, -100%)'
            }}
          >
            <div className="flex items-center space-x-1">
              <div 
                className="w-0.5 h-6 animate-pulse"
                style={{ backgroundColor: collaborator.color }}
              />
              <div 
                className="px-2 py-1 rounded text-xs text-white font-medium"
                style={{ backgroundColor: collaborator.color }}
              >
                {collaborator.name}
              </div>
            </div>
          </div>
        ))}

        {/* Main Editor */}
        <div className="h-full flex">
          <div className="flex-1 p-6">
            <div
              ref={editorRef}
              contentEditable
              className="min-h-full outline-none prose prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: mockContent }}
              onMouseUp={handleTextSelection}
              onKeyUp={handleTextSelection}
              style={{ 
                fontFamily: 'Inter, sans-serif',
                lineHeight: '1.7',
                color: 'var(--color-text-primary)'
              }}
            />
          </div>

          {/* AI Suggestions Panel */}
          {showSuggestions && (
            <div className="w-80 border-l border-border bg-background p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-text-primary">AI Suggestions</h3>
                <Button
                  variant="ghost"
                  onClick={() => setShowSuggestions(false)}
                  className="w-6 h-6 p-0"
                >
                  <Icon name="X" size={14} />
                </Button>
              </div>

              <div className="space-y-3">
                {aiSuggestions.map((suggestion) => (
                  <div key={suggestion.id} className="p-3 bg-surface rounded-lg border border-border">
                    <div className="flex items-start space-x-2 mb-2">
                      <div className={`p-1 rounded ${
                        suggestion.type === 'grammar' ? 'bg-error/10' :
                        suggestion.type === 'style' ? 'bg-warning/10' : 'bg-accent/10'
                      }`}>
                        <Icon 
                          name={
                            suggestion.type === 'grammar' ? 'AlertCircle' :
                            suggestion.type === 'style' ? 'Edit3' : 'Lightbulb'
                          } 
                          size={12}
                          color={
                            suggestion.type === 'grammar' ? 'var(--color-error)' :
                            suggestion.type === 'style' ? 'var(--color-warning)' : 'var(--color-accent)'
                          }
                        />
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-text-primary capitalize">
                          {suggestion.type}
                        </p>
                      </div>
                    </div>
                    
                    <p className="text-sm text-text-secondary mb-3">
                      {suggestion.text}
                    </p>
                    
                    <div className="flex space-x-2">
                      <Button
                        variant="success"
                        onClick={() => handleAcceptSuggestion(suggestion)}
                        className="text-xs px-2 py-1"
                      >
                        Accept
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={() => handleRejectSuggestion(suggestion)}
                        className="text-xs px-2 py-1"
                      >
                        Dismiss
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Status Bar */}
      <div className="border-t border-border px-6 py-2 bg-background">
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <div className="flex items-center space-x-4">
            <span>Words: 247</span>
            <span>Characters: 1,423</span>
            <span>Reading time: 2 min</span>
          </div>
          
          <div className="flex items-center space-x-4">
            {activeCollaborators.length > 0 && (
              <div className="flex items-center space-x-2">
                <div className="flex -space-x-1">
                  {activeCollaborators.map((collaborator) => (
                    <div
                      key={collaborator.id}
                      className="w-6 h-6 rounded-full border-2 border-surface flex items-center justify-center text-xs font-medium text-white"
                      style={{ backgroundColor: collaborator.color }}
                      title={collaborator.name}
                    >
                      {collaborator.name.charAt(0)}
                    </div>
                  ))}
                </div>
                <span className="text-xs">
                  {activeCollaborators.length} collaborator{activeCollaborators.length > 1 ? 's' : ''} online
                </span>
              </div>
            )}
            
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-success rounded-full"></div>
              <span>Auto-saved</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RichTextEditor;
import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const PlagiarismPanel = ({ isScanning, results, onStartScan }) => {
  const [selectedMatch, setSelectedMatch] = useState(null);
  
  const mockResults = results || {
    overallScore: 15,
    totalMatches: 8,
    lastScan: new Date(Date.now() - 5 * 60 * 1000),
    matches: [
      {
        id: 1,
        text: "Digital marketing has revolutionized the way businesses connect with their customers",
        similarity: 85,
        source: "Digital Marketing Guide 2023",
        url: "https://marketingguide.com/digital-basics",
        type: "exact",
        position: { start: 45, end: 125 }
      },
      {
        id: 2,
        text: "traditional marketing methods are being supplemented and often replaced",
        similarity: 72,
        source: "Modern Marketing Trends",
        url: "https://trends.marketing/modern-approaches",
        type: "paraphrase",
        position: { start: 180, end: 250 }
      },
      {
        id: 3,
        text: "Search Engine Optimization (SEO) - Optimizing content to rank higher",
        similarity: 68,
        source: "SEO Fundamentals Handbook",
        url: "https://seohandbook.com/basics",
        type: "partial",
        position: { start: 320, end: 385 }
      },
      {
        id: 4,
        text: "The best marketing doesn't feel like marketing",
        similarity: 95,
        source: "Tom Fishburne Quote Database",
        url: "https://quotes.marketing/fishburne",
        type: "quote",
        position: { start: 450, end: 495 }
      }
    ]
  };

  const getScoreColor = (score) => {
    if (score <= 10) return 'text-success';
    if (score <= 25) return 'text-warning';
    return 'text-error';
  };

  const getScoreBgColor = (score) => {
    if (score <= 10) return 'bg-success/10 border-success/20';
    if (score <= 25) return 'bg-warning/10 border-warning/20';
    return 'bg-error/10 border-error/20';
  };

  const getMatchTypeIcon = (type) => {
    switch (type) {
      case 'exact':
        return { name: 'AlertTriangle', color: 'var(--color-error)' };
      case 'paraphrase':
        return { name: 'AlertCircle', color: 'var(--color-warning)' };
      case 'partial':
        return { name: 'Info', color: 'var(--color-secondary)' };
      case 'quote':
        return { name: 'Quote', color: 'var(--color-accent)' };
      default:
        return { name: 'FileText', color: 'var(--color-text-secondary)' };
    }
  };

  const getMatchTypeLabel = (type) => {
    switch (type) {
      case 'exact':
        return 'Exact Match';
      case 'paraphrase':
        return 'Paraphrased';
      case 'partial':
        return 'Partial Match';
      case 'quote':
        return 'Quote/Citation';
      default:
        return 'Similar';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    return `${hours}h ago`;
  };

  const handleViewSource = (match) => {
    window.open(match.url, '_blank');
  };

  const handleIgnoreMatch = (matchId) => {
    console.log('Ignoring match:', matchId);
    // Implementation would remove match from results
  };

  const handleRephraseSuggestion = (match) => {
    console.log('Getting rephrase suggestions for:', match.text);
    // Implementation would show AI rephrase suggestions
  };

  return (
    <div className="h-full flex flex-col bg-surface">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-text-primary">Plagiarism Check</h3>
          <Button
            variant="primary"
            onClick={onStartScan}
            disabled={isScanning}
            iconName={isScanning ? "Loader2" : "Shield"}
            className={`text-sm ${isScanning ? 'animate-spin' : ''}`}
          >
            {isScanning ? 'Scanning...' : 'Scan Now'}
          </Button>
        </div>

        {/* Overall Score */}
        {mockResults && !isScanning && (
          <div className={`p-4 rounded-lg border ${getScoreBgColor(mockResults.overallScore)}`}>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center space-x-2">
                  <span className={`text-2xl font-bold ${getScoreColor(mockResults.overallScore)}`}>
                    {mockResults.overallScore}%
                  </span>
                  <span className="text-sm text-text-secondary">similarity found</span>
                </div>
                <p className="text-xs text-text-secondary mt-1">
                  Last scan: {formatTimestamp(mockResults.lastScan)}
                </p>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-text-primary">
                  {mockResults.totalMatches} matches
                </p>
                <p className="text-xs text-text-secondary">
                  {mockResults.overallScore <= 10 ? 'Excellent' : 
                   mockResults.overallScore <= 25 ? 'Good' : 'Needs Review'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Scanning Progress */}
        {isScanning && (
          <div className="p-4 bg-secondary/10 rounded-lg border border-secondary/20">
            <div className="flex items-center space-x-3">
              <div className="animate-spin">
                <Icon name="Loader2" size={16} color="var(--color-secondary)" />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-text-primary">Scanning document...</p>
                <p className="text-xs text-text-secondary">Checking against web sources and databases</p>
              </div>
            </div>
            <div className="mt-3">
              <div className="w-full bg-border rounded-full h-2">
                <div className="bg-secondary h-2 rounded-full animate-pulse" style={{ width: '65%' }}></div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Matches List */}
      {mockResults && !isScanning && (
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 space-y-3">
            {mockResults.matches.map((match) => {
              const typeConfig = getMatchTypeIcon(match.type);
              return (
                <div
                  key={match.id}
                  className={`p-4 rounded-lg border transition-micro cursor-pointer ${
                    selectedMatch === match.id 
                      ? 'border-primary bg-primary/5' :'border-border hover:border-primary/50'
                  }`}
                  onClick={() => setSelectedMatch(selectedMatch === match.id ? null : match.id)}
                >
                  {/* Match Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className="p-1 rounded bg-background">
                        <Icon name={typeConfig.name} size={14} color={typeConfig.color} />
                      </div>
                      <div>
                        <span className="text-sm font-medium text-text-primary">
                          {getMatchTypeLabel(match.type)}
                        </span>
                        <span className={`ml-2 text-sm font-bold ${getScoreColor(match.similarity)}`}>
                          {match.similarity}%
                        </span>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedMatch(selectedMatch === match.id ? null : match.id);
                      }}
                      className="w-6 h-6 p-0"
                    >
                      <Icon name={selectedMatch === match.id ? "ChevronUp" : "ChevronDown"} size={14} />
                    </Button>
                  </div>

                  {/* Matched Text */}
                  <div className="mb-3">
                    <p className="text-sm text-text-primary bg-warning/10 p-2 rounded border-l-4 border-warning">
                      "{match.text}"
                    </p>
                  </div>

                  {/* Source Info */}
                  <div className="flex items-center justify-between text-xs text-text-secondary">
                    <span className="truncate flex-1 mr-2">
                      Source: {match.source}
                    </span>
                    <Button
                      variant="ghost"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewSource(match);
                      }}
                      className="text-xs px-2 py-1"
                    >
                      View Source
                    </Button>
                  </div>

                  {/* Expanded Actions */}
                  {selectedMatch === match.id && (
                    <div className="mt-4 pt-3 border-t border-border">
                      <div className="flex flex-wrap gap-2">
                        <Button
                          variant="outline"
                          onClick={() => handleRephraseSuggestion(match)}
                          iconName="Edit3"
                          className="text-xs px-3 py-1"
                        >
                          Rephrase
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => console.log('Add citation for:', match.id)}
                          iconName="Quote"
                          className="text-xs px-3 py-1"
                        >
                          Cite Source
                        </Button>
                        <Button
                          variant="ghost"
                          onClick={() => handleIgnoreMatch(match.id)}
                          iconName="X"
                          className="text-xs px-3 py-1 text-text-secondary"
                        >
                          Ignore
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* Empty State */}
      {!mockResults && !isScanning && (
        <div className="flex-1 flex items-center justify-center p-8">
          <div className="text-center">
            <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="Shield" size={32} color="var(--color-secondary)" />
            </div>
            <h4 className="text-lg font-medium text-text-primary mb-2">No Scan Results</h4>
            <p className="text-sm text-text-secondary mb-4">
              Click "Scan Now" to check your document for plagiarism
            </p>
            <Button
              variant="primary"
              onClick={onStartScan}
              iconName="Shield"
            >
              Start Plagiarism Check
            </Button>
          </div>
        </div>
      )}

      {/* Footer Actions */}
      {mockResults && !isScanning && (
        <div className="p-4 border-t border-border">
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              onClick={() => console.log('Export report')}
              iconName="Download"
              className="text-xs"
            >
              Export Report
            </Button>
            <Button
              variant="outline"
              onClick={() => console.log('Share results')}
              iconName="Share2"
              className="text-xs"
            >
              Share Results
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PlagiarismPanel;
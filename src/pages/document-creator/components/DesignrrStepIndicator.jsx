import React from 'react';
import Icon from '../../../components/AppIcon';

const DesignrrStepIndicator = ({ steps, currentStep, onStepClick, className = '' }) => {
  return (
    <div className={`w-full ${className}`}>
      {/* Designrr-style horizontal step indicator */}
      <div className="flex items-center justify-center space-x-4 md:space-x-8 py-6 px-4">
        {steps.map((step, index) => {
          const isActive = currentStep === step.id;
          const isCompleted = currentStep > step.id;
          const isClickable = currentStep >= step.id;

          return (
            <React.Fragment key={step.id}>
              {/* Step Item */}
              <div className="flex items-center space-x-2 md:space-x-3">
                {/* Step Number Circle */}
                <button
                  onClick={() => isClickable && onStepClick && onStepClick(step.id)}
                  disabled={!isClickable}
                  className={`
                    w-8 h-8 rounded-full flex items-center justify-center text-sm font-semibold
                    transition-all duration-300 relative z-10
                    ${isActive
                      ? 'bg-primary text-white shadow-md'
                      : isCompleted
                        ? 'bg-primary text-white'
                        : 'bg-gray-200 text-gray-500'
                    }
                    ${isClickable ? 'cursor-pointer hover:shadow-lg' : 'cursor-not-allowed'}
                  `}
                >
                  {isCompleted ? (
                    <Icon name="Check" size={14} />
                  ) : (
                    <span>{step.id}</span>
                  )}
                </button>

                {/* Step Label - Hidden on mobile */}
                <div className="text-left hidden sm:block">
                  <div className={`text-sm font-medium ${
                    isActive ? 'text-primary' : isCompleted ? 'text-text-primary' : 'text-text-secondary'
                  }`}>
                    {step.title}
                  </div>
                </div>
              </div>

              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div
                  className={`
                    h-0.5 w-8 md:w-12 transition-all duration-500
                    ${isCompleted ? 'bg-primary' : 'bg-gray-200'}
                  `}
                />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default DesignrrStepIndicator;

import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Image from '../../../components/AppImage';

const VisualSuggestions = ({ onInsertImage, currentContext }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);

  const categories = [
    { id: 'all', name: 'All', icon: 'Grid3X3' },
    { id: 'business', name: 'Business', icon: 'Briefcase' },
    { id: 'technology', name: 'Technology', icon: 'Smartphone' },
    { id: 'education', name: 'Education', icon: 'GraduationCap' },
    { id: 'marketing', name: 'Marketing', icon: 'TrendingUp' },
    { id: 'people', name: 'People', icon: 'Users' }
  ];

  const mockImages = [
    {
      id: 1,
      url: 'https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?auto=compress&cs=tinysrgb&w=400',
      title: 'Digital Marketing Strategy',
      source: 'Pexels',
      photographer: 'Fauxels',
      category: 'marketing',
      tags: ['marketing', 'strategy', 'digital', 'business'],
      size: { width: 400, height: 267 }
    },
    {
      id: 2,
      url: 'https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?auto=compress&cs=tinysrgb&w=400',
      title: 'Team Collaboration',
      source: 'Pexels',
      photographer: 'Fauxels',
      category: 'business',
      tags: ['team', 'collaboration', 'meeting', 'business'],
      size: { width: 400, height: 267 }
    },
    {
      id: 3,
      url: 'https://images.pixabay.com/photo/2016/11/19/14/00/code-1839406_960_720.jpg?auto=compress&cs=tinysrgb&w=400',
      title: 'Code Development',
      source: 'Pixabay',
      photographer: 'Pixabay',
      category: 'technology',
      tags: ['code', 'programming', 'development', 'technology'],
      size: { width: 400, height: 267 }
    },
    {
      id: 4,
      url: 'https://images.unsplash.com/photo-1552664730-d307ca884978?auto=compress&cs=tinysrgb&w=400',
      title: 'Business Analytics',
      source: 'Unsplash',
      photographer: 'Isaac Smith',
      category: 'business',
      tags: ['analytics', 'charts', 'data', 'business'],
      size: { width: 400, height: 267 }
    },
    {
      id: 5,
      url: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=400',
      title: 'Online Learning',
      source: 'Pexels',
      photographer: 'Julia M Cameron',
      category: 'education',
      tags: ['education', 'learning', 'online', 'student'],
      size: { width: 400, height: 267 }
    },
    {
      id: 6,
      url: 'https://images.pixabay.com/photo/2016/11/29/06/15/plans-1867745_960_720.jpg?auto=compress&cs=tinysrgb&w=400',
      title: 'Strategic Planning',
      source: 'Pixabay',
      photographer: 'StartupStockPhotos',
      category: 'business',
      tags: ['planning', 'strategy', 'business', 'documents'],
      size: { width: 400, height: 267 }
    },
    {
      id: 7,
      url: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=compress&cs=tinysrgb&w=400',
      title: 'Data Visualization',
      source: 'Unsplash',
      photographer: 'Carlos Muza',
      category: 'technology',
      tags: ['data', 'visualization', 'charts', 'analytics'],
      size: { width: 400, height: 267 }
    },
    {
      id: 8,
      url: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=400',
      title: 'Creative Team',
      source: 'Pexels',
      photographer: 'Fauxels',
      category: 'people',
      tags: ['team', 'creative', 'collaboration', 'people'],
      size: { width: 400, height: 267 }
    }
  ];

  const contextualSuggestions = [
    'digital marketing',
    'business strategy',
    'team collaboration',
    'data analytics',
    'content creation'
  ];

  const filteredImages = mockImages.filter(image => {
    const matchesCategory = selectedCategory === 'all' || image.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      image.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      image.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const handleSearch = (e) => {
    e.preventDefault();
    setIsLoading(true);
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  const handleQuickSearch = (query) => {
    setSearchQuery(query);
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
    }, 800);
  };

  const handleImageSelect = (image) => {
    setSelectedImage(selectedImage?.id === image.id ? null : image);
  };

  const handleInsertImage = (image) => {
    onInsertImage(image);
    setSelectedImage(null);
  };

  return (
    <div className="h-full flex flex-col bg-surface">
      {/* Header */}
      <div className="p-4 border-b border-border">
        <h3 className="text-lg font-semibold text-text-primary mb-4">Visual Suggestions</h3>
        
        {/* Search */}
        <form onSubmit={handleSearch} className="mb-4">
          <div className="relative">
            <Input
              type="search"
              placeholder="Search for images..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pr-10"
            />
            <Button
              type="submit"
              variant="ghost"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-6 h-6 p-0"
            >
              <Icon name="Search" size={16} />
            </Button>
          </div>
        </form>

        {/* Quick Suggestions */}
        <div className="mb-4">
          <p className="text-sm font-medium text-text-primary mb-2">Suggested for your content:</p>
          <div className="flex flex-wrap gap-2">
            {contextualSuggestions.map((suggestion) => (
              <Button
                key={suggestion}
                variant="outline"
                onClick={() => handleQuickSearch(suggestion)}
                className="text-xs px-2 py-1"
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>

        {/* Categories */}
        <div className="flex space-x-1 overflow-x-auto pb-2">
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "primary" : "ghost"}
              onClick={() => setSelectedCategory(category.id)}
              className="flex-shrink-0 text-xs px-3 py-1"
              iconName={category.icon}
              iconPosition="left"
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Images Grid */}
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="grid grid-cols-2 gap-3">
            {[...Array(6)].map((_, index) => (
              <div key={index} className="aspect-video bg-background rounded-lg animate-pulse" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-2 gap-3">
            {filteredImages.map((image) => (
              <div
                key={image.id}
                className={`group relative cursor-pointer rounded-lg overflow-hidden border-2 transition-micro ${
                  selectedImage?.id === image.id 
                    ? 'border-primary' :'border-transparent hover:border-primary/50'
                }`}
                onClick={() => handleImageSelect(image)}
              >
                <div className="aspect-video">
                  <Image
                    src={image.url}
                    alt={image.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                
                {/* Overlay */}
                <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-micro" />
                
                {/* Action Buttons */}
                <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleInsertImage(image);
                    }}
                    className="w-8 h-8 p-0 bg-surface/90 hover:bg-surface"
                  >
                    <Icon name="Plus" size={16} />
                  </Button>
                </div>

                {/* Image Info */}
                <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/60 to-transparent">
                  <p className="text-white text-xs font-medium truncate">
                    {image.title}
                  </p>
                  <p className="text-white/80 text-xs">
                    {image.source} • {image.photographer}
                  </p>
                </div>

                {/* Selection Indicator */}
                {selectedImage?.id === image.id && (
                  <div className="absolute top-2 left-2">
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                      <Icon name="Check" size={14} color="white" />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Empty State */}
        {!isLoading && filteredImages.length === 0 && (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-background rounded-full flex items-center justify-center mx-auto mb-4">
              <Icon name="Image" size={32} color="var(--color-text-secondary)" />
            </div>
            <h4 className="text-lg font-medium text-text-primary mb-2">No Images Found</h4>
            <p className="text-sm text-text-secondary">
              Try adjusting your search terms or category filter
            </p>
          </div>
        )}
      </div>

      {/* Selected Image Actions */}
      {selectedImage && (
        <div className="p-4 border-t border-border bg-background">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-12 h-8 rounded overflow-hidden">
              <Image
                src={selectedImage.url}
                alt={selectedImage.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium text-text-primary">
                {selectedImage.title}
              </p>
              <p className="text-xs text-text-secondary">
                {selectedImage.size.width} × {selectedImage.size.height}
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="primary"
              onClick={() => handleInsertImage(selectedImage)}
              iconName="Plus"
              className="text-sm"
            >
              Insert Image
            </Button>
            <Button
              variant="outline"
              onClick={() => window.open(selectedImage.url, '_blank')}
              iconName="ExternalLink"
              className="text-sm"
            >
              View Full Size
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VisualSuggestions;
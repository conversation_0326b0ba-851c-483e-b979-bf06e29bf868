import React, { useState, useEffect, useRef } from 'react';
import Icon from '../../../components/AppIcon';

const ProjectCard = ({
  project,
  onClick,
  onPreview,
  onDuplicate,
  onShare,
  onDelete
}) => {
  const [showActions, setShowActions] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowActions(false);
      }
    };

    if (showActions) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showActions]);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'long',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div
      className="bg-surface rounded-md border border-border hover:shadow-elevated transition-all duration-300 cursor-pointer group overflow-hidden"
      onClick={() => onClick(project)}
    >
      {/* Project Thumbnail */}
      <div className="aspect-[4/3] bg-gradient-to-br from-gray-100 to-gray-200 relative overflow-hidden">
        <img
          src={project.thumbnail}
          alt={project.title}
          className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
        />

        {/* Status Badge */}
        <div className={`absolute top-3 left-3 px-2 py-1 rounded-lg text-xs font-medium shadow-sm ${
          project.status === 'completed' ? 'bg-success text-white' :
          project.status === 'draft' ? 'bg-warning text-white' :
          'bg-primary text-white'
        }`}>
          {project.status.charAt(0).toUpperCase() + project.status.slice(1)}
        </div>

        {/* Hover Actions */}
        <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
          <div className="flex items-center space-x-2">
            <button
              className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onPreview && onPreview(project);
              }}
              title="Preview"
            >
              <Icon name="Eye" size={16} />
            </button>
            <button
              className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
              onClick={(e) => {
                e.stopPropagation();
                onClick(project);
              }}
              title="Edit"
            >
              <Icon name="Edit" size={16} />
            </button>
            <div className="relative" ref={dropdownRef}>
              <button
                className="bg-white text-text-primary p-2 rounded-lg shadow-sm hover:bg-gray-50 transition-colors"
                onClick={(e) => {
                  e.stopPropagation();
                  setShowActions(!showActions);
                }}
                title="More actions"
              >
                <Icon name="MoreVertical" size={16} />
              </button>

              {/* Actions Dropdown */}
              {showActions && (
                <div className="absolute right-0 top-full mt-1 bg-white rounded-lg shadow-lg border border-border py-1 z-10 min-w-[120px]">
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-text-primary hover:bg-surface-hover flex items-center space-x-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDuplicate && onDuplicate(project);
                      setShowActions(false);
                    }}
                  >
                    <Icon name="Copy" size={14} />
                    <span>Duplicate</span>
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-text-primary hover:bg-surface-hover flex items-center space-x-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      onShare && onShare(project);
                      setShowActions(false);
                    }}
                  >
                    <Icon name="Share" size={14} />
                    <span>Share</span>
                  </button>
                  <button
                    className="w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive hover:text-white flex items-center space-x-2"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete && onDelete(project);
                      setShowActions(false);
                    }}
                  >
                    <Icon name="Trash2" size={14} />
                    <span>Delete</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Project Info */}
      <div className="p-4">
        <h4 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300 line-clamp-2 mb-2">
          {project.title}
        </h4>
        <div className="flex items-center justify-between text-sm text-text-secondary">
          <span className="capitalize">{project.type}</span>
          <span>{formatDate(project.updatedAt)}</span>
        </div>

        {project.progress < 100 && (
          <div className="mt-3">
            <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
              <span>Progress</span>
              <span>{project.progress}%</span>
            </div>
            <div className="w-full bg-surface-secondary rounded-full h-2">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${project.progress}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Collaborators */}
        {project.collaborators && project.collaborators.length > 0 && (
          <div className="mt-3 flex items-center">
            <div className="flex -space-x-2">
              {project.collaborators.slice(0, 3).map((collaborator, index) => (
                <div
                  key={index}
                  className="w-6 h-6 rounded-full bg-primary text-white text-xs flex items-center justify-center border-2 border-surface"
                  title={collaborator.name}
                >
                  {collaborator.name.charAt(0).toUpperCase()}
                </div>
              ))}
              {project.collaborators.length > 3 && (
                <div className="w-6 h-6 rounded-full bg-surface-secondary text-text-secondary text-xs flex items-center justify-center border-2 border-surface">
                  +{project.collaborators.length - 3}
                </div>
              )}
            </div>
            <span className="ml-2 text-xs text-text-secondary">
              {project.collaborators.length} collaborator{project.collaborators.length !== 1 ? 's' : ''}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProjectCard;

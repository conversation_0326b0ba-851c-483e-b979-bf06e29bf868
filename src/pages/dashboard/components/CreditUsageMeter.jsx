import React from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CreditUsageMeter = ({ currentCredits, totalCredits, usageHistory }) => {
  const navigate = useNavigate();
  const usagePercentage = (currentCredits / totalCredits) * 100;
  const isLowCredits = usagePercentage < 20;

  const getUsageColor = () => {
    if (usagePercentage > 50) return 'bg-success';
    if (usagePercentage > 20) return 'bg-warning';
    return 'bg-error';
  };

  const handleUpgradeClick = () => {
    navigate('/account-settings', { state: { tab: 'billing' } });
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6 shadow-elevation-1">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-text-primary">Credit Usage</h3>
        <Icon name="Zap" size={20} color="var(--color-accent)" />
      </div>

      {/* Credit Balance */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-text-secondary">Available Credits</span>
          <span className="text-lg font-bold text-text-primary">
            {currentCredits.toLocaleString()}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-border rounded-full h-3 mb-2">
          <div
            className={`h-3 rounded-full transition-all duration-500 ${getUsageColor()}`}
            style={{ width: `${usagePercentage}%` }}
          />
        </div>
        
        <div className="flex items-center justify-between text-xs text-text-secondary">
          <span>0</span>
          <span>{totalCredits.toLocaleString()} total</span>
        </div>
      </div>

      {/* Low Credits Warning */}
      {isLowCredits && (
        <div className="bg-warning/10 border border-warning/20 rounded-lg p-3 mb-4">
          <div className="flex items-center space-x-2">
            <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
            <span className="text-sm font-medium text-warning">Low Credit Balance</span>
          </div>
          <p className="text-xs text-text-secondary mt-1">
            Consider upgrading your plan to continue using AI features.
          </p>
        </div>
      )}

      {/* Usage Breakdown */}
      <div className="space-y-3 mb-4">
        <h4 className="text-sm font-medium text-text-primary">Recent Usage</h4>
        {usageHistory.map((usage, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Icon name={usage.icon} size={14} className="text-text-secondary" />
              <span className="text-sm text-text-secondary">{usage.activity}</span>
            </div>
            <span className="text-sm font-medium text-text-primary">
              -{usage.credits}
            </span>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="space-y-2">
        <Button
          variant="primary"
          onClick={handleUpgradeClick}
          className="w-full"
          iconName="CreditCard"
        >
          Upgrade Plan
        </Button>
        <Button
          variant="outline"
          onClick={() => console.log('View usage history')}
          className="w-full"
          iconName="BarChart3"
        >
          View Usage History
        </Button>
      </div>
    </div>
  );
};

export default CreditUsageMeter;
import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import '@testing-library/jest-dom';
import DocumentLibrary from '../DocumentLibrary';

// Mock the navigation hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

// Mock the Icon component
jest.mock('../../../../components/AppIcon', () => {
  return function MockIcon({ name, size, variant, className }) {
    return <div data-testid={`icon-${name}`} className={className}>{name}</div>;
  };
});

// Mock the Button component
jest.mock('../../../../components/ui/Button', () => {
  return function MockButton({ children, onClick, variant, iconName, ...props }) {
    return (
      <button onClick={onClick} data-variant={variant} data-icon={iconName} {...props}>
        {children}
      </button>
    );
  };
});

// Mock the Input component
jest.mock('../../../../components/ui/Input', () => {
  return function MockInput({ onChange, value, placeholder, ...props }) {
    return (
      <input 
        onChange={onChange} 
        value={value} 
        placeholder={placeholder} 
        {...props} 
      />
    );
  };
});

// Mock the DocumentCard component
jest.mock('../DocumentCard', () => {
  return function MockDocumentCard({ document, compact }) {
    return (
      <div data-testid={`document-card-${document.id}`} data-compact={compact}>
        {document.title}
      </div>
    );
  };
});

const mockDocuments = [
  {
    id: 1,
    title: 'Test Document 1',
    type: 'business',
    status: 'completed',
    createdAt: '2024-01-15T10:30:00Z',
    pageCount: 10,
  },
  {
    id: 2,
    title: 'Test Document 2',
    type: 'academic',
    status: 'draft',
    createdAt: '2024-01-16T10:30:00Z',
    pageCount: 5,
  },
];

const defaultProps = {
  documents: mockDocuments,
  onEdit: jest.fn(),
  onDuplicate: jest.fn(),
  onShare: jest.fn(),
  onDelete: jest.fn(),
};

const renderWithRouter = (component) => {
  return render(
    <BrowserRouter>
      {component}
    </BrowserRouter>
  );
};

describe('DocumentLibrary Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Conditional Rendering - Empty Documents', () => {
    it('should render empty state when no documents and not compact', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} documents={[]} compact={false} />
      );

      expect(screen.getByText('No documents yet')).toBeInTheDocument();
      expect(screen.getByText('Start creating your first document with AI assistance.')).toBeInTheDocument();
      expect(screen.getByText('Create Your First Document')).toBeInTheDocument();
    });

    it('should render null when no documents and compact mode', () => {
      const { container } = renderWithRouter(
        <DocumentLibrary {...defaultProps} documents={[]} compact={true} />
      );

      // In compact mode with no documents, the component should render minimal content
      expect(screen.queryByText('No documents yet')).not.toBeInTheDocument();
      expect(screen.queryByText('Create Your First Document')).not.toBeInTheDocument();
    });

    it('should render search-specific empty state when search query returns no results', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} documents={[]} compact={false} />
      );

      // Simulate a search that returns no results
      const searchInput = screen.getByPlaceholderText('Search documents...');
      searchInput.value = 'nonexistent';
      
      // The component should show search-specific empty state
      expect(screen.getByText('No documents yet')).toBeInTheDocument();
    });
  });

  describe('Conditional Rendering - With Documents', () => {
    it('should render document grid when documents exist and not compact', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={false} />
      );

      expect(screen.getByTestId('document-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('document-card-2')).toBeInTheDocument();
      expect(screen.getByText('Test Document 1')).toBeInTheDocument();
      expect(screen.getByText('Test Document 2')).toBeInTheDocument();
    });

    it('should render document list when documents exist and compact mode', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={true} />
      );

      const card1 = screen.getByTestId('document-card-1');
      const card2 = screen.getByTestId('document-card-2');
      
      expect(card1).toBeInTheDocument();
      expect(card2).toBeInTheDocument();
      expect(card1).toHaveAttribute('data-compact', 'true');
      expect(card2).toHaveAttribute('data-compact', 'true');
    });
  });

  describe('Compact Mode Behavior', () => {
    it('should hide header when in compact mode', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={true} />
      );

      expect(screen.queryByText('Document Library')).not.toBeInTheDocument();
      expect(screen.queryByText('Manage and organize your documents')).not.toBeInTheDocument();
    });

    it('should show header when not in compact mode', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={false} />
      );

      expect(screen.getByText('Document Library')).toBeInTheDocument();
      expect(screen.getByText('Manage and organize your documents')).toBeInTheDocument();
    });

    it('should hide filters when in compact mode', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={true} />
      );

      expect(screen.queryByText('All Documents')).not.toBeInTheDocument();
      expect(screen.queryByPlaceholderText('Search documents...')).not.toBeInTheDocument();
    });

    it('should show filters when not in compact mode and showFilters is true', () => {
      renderWithRouter(
        <DocumentLibrary {...defaultProps} compact={false} showFilters={true} />
      );

      expect(screen.getByText('All Documents')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('Search documents...')).toBeInTheDocument();
    });
  });

  describe('Props Validation', () => {
    it('should handle missing documents prop gracefully', () => {
      const propsWithoutDocuments = { ...defaultProps };
      delete propsWithoutDocuments.documents;

      expect(() => {
        renderWithRouter(<DocumentLibrary {...propsWithoutDocuments} />);
      }).toThrow();
    });

    it('should use default values for optional props', () => {
      const minimalProps = {
        documents: mockDocuments,
        onEdit: jest.fn(),
        onDuplicate: jest.fn(),
        onShare: jest.fn(),
        onDelete: jest.fn(),
      };

      renderWithRouter(<DocumentLibrary {...minimalProps} />);

      // Should render in non-compact mode by default
      expect(screen.getByText('Document Library')).toBeInTheDocument();
      expect(screen.getByText('All Documents')).toBeInTheDocument();
    });
  });
});

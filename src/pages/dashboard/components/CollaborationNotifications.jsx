import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const CollaborationNotifications = ({ notifications, onAccept, onDecline, onViewAll, compact = false }) => {
  const [processingId, setProcessingId] = useState(null);
  const navigate = useNavigate();

  const handleAccept = async (notification) => {
    setProcessingId(notification.id);
    try {
      await onAccept(notification);
      navigate('/collaboration-workspace');
    } catch (error) {
      console.error('Failed to accept invitation:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const handleDecline = async (notification) => {
    setProcessingId(notification.id);
    try {
      await onDecline(notification);
    } catch (error) {
      console.error('Failed to decline invitation:', error);
    } finally {
      setProcessingId(null);
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - new Date(timestamp);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (hours < 1) return 'Just now';
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className={compact ? "" : "bg-white rounded-xl border border-border p-6 shadow-card"}>
      {!compact && (
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text-primary">Collaboration</h3>
          <div className="flex items-center space-x-2">
            {notifications.length > 0 && (
              <span className="bg-accent-light text-accent text-xs px-2 py-1 rounded-full font-medium">
              {notifications.length}
            </span>
          )}
            <Icon name="Users" size={20} variant="secondary" />
          </div>
        </div>
      )}

      <div className={compact ? "space-y-3" : "space-y-4"}>
        {notifications.length === 0 ? (
          !compact && (
            <div className="text-center py-8">
              <Icon name="Users" size={32} variant="muted" className="mx-auto mb-2" />
              <p className="text-sm text-text-secondary mb-2">No collaboration invites</p>
              <Button
                variant="secondary"
                onClick={() => navigate('/collaboration-workspace')}
                iconName="Plus"
                size="sm"
              >
                Start Collaborating
              </Button>
            </div>
          )
        ) : (
          <>
            {notifications.slice(0, compact ? 2 : 3).map((notification) => (
              <div key={notification.id} className={`${compact ? 'p-3 bg-surface-secondary' : 'border border-border p-4'} rounded-lg`}>
                <div className="flex items-start space-x-3">
                  <div className="w-8 h-8 rounded-full bg-primary-light flex items-center justify-center text-primary font-medium text-sm">
                    {notification.inviterName.charAt(0)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-text-primary">
                      {notification.inviterName}
                    </p>
                    <p className="text-xs text-text-secondary mb-2">
                      invited you to collaborate on "{notification.documentTitle}"
                    </p>
                    <p className="text-xs text-text-secondary">
                      {formatTimeAgo(notification.timestamp)}
                    </p>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mt-3">
                  <button
                    onClick={() => handleAccept(notification)}
                    disabled={processingId === notification.id}
                    className="px-3 py-1 bg-primary text-white text-xs rounded-lg hover:bg-primary/90 transition-colors duration-300 disabled:opacity-50"
                  >
                    {processingId === notification.id ? 'Accepting...' : 'Accept'}
                  </button>
                  <button
                    onClick={() => handleDecline(notification)}
                    disabled={processingId === notification.id}
                    className="px-3 py-1 bg-surface-hover text-text-secondary text-xs rounded-lg hover:bg-border-strong transition-colors duration-300 disabled:opacity-50"
                  >
                    Decline
                  </button>
                </div>
              </div>
            ))}
            
            {notifications.length > 3 && (
              <Button
                variant="ghost"
                onClick={onViewAll}
                className="w-full"
                iconName="ChevronRight"
                iconPosition="right"
              >
                View All Invitations ({notifications.length})
              </Button>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CollaborationNotifications;
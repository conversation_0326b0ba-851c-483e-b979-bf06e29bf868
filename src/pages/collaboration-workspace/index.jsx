import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import CollaboratorPanel from './components/CollaboratorPanel';
import ChatPanel from './components/ChatPanel';
import DocumentEditor from './components/DocumentEditor';
import VersionHistory from './components/VersionHistory';
import TaskPanel from './components/TaskPanel';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const CollaborationWorkspace = () => {
  const navigate = useNavigate();
  const { contentMargin } = useSidebar();
  const [activeTab, setActiveTab] = useState('collaborators');
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  // Mock current user
  const currentUser = {
    id: 'current-user',
    name: 'John Doe',
    email: '<EMAIL>',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
  };

  // Mock document data
  const [document] = useState({
    id: 'doc-1',
    title: 'Market Analysis Report 2024',
    content: `<h1>Market Analysis Report 2024</h1>
    <h2>Executive Summary</h2>
    <p>This comprehensive market analysis provides insights into current market trends, competitive landscape, and growth opportunities for the upcoming fiscal year.</p>
    
    <h2>Market Overview</h2>
    <p>The global market has shown significant resilience despite economic uncertainties. Key indicators suggest a positive trajectory with emerging opportunities in digital transformation and sustainable technologies.</p>
    
    <h3>Key Findings</h3>
    <ul>
      <li>Market growth rate of 12.5% year-over-year</li>
      <li>Increased adoption of AI-driven solutions</li>
      <li>Shift towards sustainable business practices</li>
      <li>Growing demand for remote collaboration tools</li>
    </ul>
    
    <h2>Competitive Analysis</h2>
    <p>Our analysis reveals three major competitors dominating the market space, each with distinct strengths and market positioning strategies.</p>`,
    status: 'In Progress',
    lastSaved: '2 minutes ago'
  });

  // Mock collaborators data
  const [collaborators, setCollaborators] = useState([
    {
      id: 'user-1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      role: 'owner',
      status: 'active',
      isTyping: false,
      lastSeen: null
    },
    {
      id: 'user-2',
      name: 'Michael Chen',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      role: 'editor',
      status: 'active',
      isTyping: true,
      lastSeen: null
    },
    {
      id: 'user-3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      role: 'commenter',
      status: 'away',
      isTyping: false,
      lastSeen: '5 minutes ago'
    },
    {
      id: 'user-4',
      name: 'David Park',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
      role: 'viewer',
      status: 'offline',
      isTyping: false,
      lastSeen: '2 hours ago'
    }
  ]);

  // Mock chat messages
  const [messages, setMessages] = useState([
    {
      id: 1,
      userId: 'user-1',
      userName: 'Sarah Johnson',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      content: 'Hey team! I\'ve added the executive summary section. Please review and add your feedback.',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      reactions: [{ emoji: '👍', count: 2 }]
    },
    {
      id: 2,
      userId: 'user-2',
      userName: 'Michael Chen',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      content: 'Great work on the market overview! I\'m working on the competitive analysis section now.',
      timestamp: new Date(Date.now() - 10 * 60 * 1000)
    },
    {
      id: 3,
      userId: 'current-user',
      userName: 'John Doe',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
      content: 'I can help with the data visualization charts. Should we include the Q3 performance metrics?',
      timestamp: new Date(Date.now() - 5 * 60 * 1000)
    }
  ]);

  // Mock version history
  const [versions] = useState([
    {
      id: 'v1.3',
      version: '1.3',
      changeType: 'minor',
      summary: 'Added competitive analysis section and updated market data',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      author: {
        name: 'Michael Chen',
        avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
      },
      changes: {
        additions: 15,
        deletions: 3,
        modifications: 8
      },
      detailedChanges: [
        { type: 'addition', description: 'Added competitive analysis section' },
        { type: 'modification', description: 'Updated market growth statistics' },
        { type: 'addition', description: 'Inserted competitor comparison table' }
      ],
      comments: [
        {
          author: { name: 'Sarah Johnson', avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face' },
          text: 'Great addition to the analysis!'
        }
      ]
    },
    {
      id: 'v1.2',
      version: '1.2',
      changeType: 'major',
      summary: 'Restructured document layout and added executive summary',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      author: {
        name: 'Sarah Johnson',
        avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
      },
      changes: {
        additions: 25,
        deletions: 12,
        modifications: 18
      },
      detailedChanges: [
        { type: 'addition', description: 'Created executive summary section' },
        { type: 'modification', description: 'Reorganized document structure' },
        { type: 'deletion', description: 'Removed outdated market data' }
      ]
    },
    {
      id: 'v1.1',
      version: '1.1',
      changeType: 'patch',
      summary: 'Fixed formatting issues and typos',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      author: {
        name: 'Emily Rodriguez',
        avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
      },
      changes: {
        additions: 2,
        deletions: 1,
        modifications: 5
      },
      detailedChanges: [
        { type: 'modification', description: 'Fixed spelling errors in introduction' },
        { type: 'modification', description: 'Corrected formatting in bullet points' }
      ]
    }
  ]);

  // Mock tasks
  const [tasks, setTasks] = useState([
    {
      id: 1,
      title: 'Review executive summary',
      description: 'Please review the executive summary section and provide feedback on key points and clarity.',
      assignee: 'user-3',
      priority: 'high',
      status: 'pending',
      type: 'review',
      dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 60 * 60 * 1000),
      createdBy: 'user-1'
    },
    {
      id: 2,
      title: 'Add financial projections',
      description: 'Include Q4 financial projections and revenue forecasts in the market analysis section.',
      assignee: 'current-user',
      priority: 'medium',
      status: 'in-progress',
      type: 'edit',
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      createdBy: 'user-1'
    },
    {
      id: 3,
      title: 'Proofread final draft',
      description: 'Complete final proofreading and grammar check before submission.',
      assignee: 'user-4',
      priority: 'low',
      status: 'completed',
      type: 'review',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      createdBy: 'user-2'
    }
  ]);

  // Mock comments
  const [comments] = useState([
    {
      text: 'Market growth rate of 12.5% year-over-year',
      comment: 'Can we verify this statistic with the latest industry reports?',
      author: 'Emily Rodriguez',
      position: { x: 100, y: 200 }
    },
    {
      text: 'digital transformation',
      comment: 'Should we elaborate on specific digital transformation trends?',
      author: 'Michael Chen',
      position: { x: 150, y: 300 }
    }
  ]);

  // Mock live cursors
  const [cursors] = useState([
    {
      userId: 'user-2',
      userName: 'Michael Chen',
      color: '#3B82F6',
      x: 200,
      y: 150
    }
  ]);

  const handleInviteUser = (email, role) => {
    console.log('Inviting user:', email, 'with role:', role);
    // In real app, send invitation
  };

  const handleChangePermission = (userId, newRole) => {
    if (newRole === 'remove') {
      setCollaborators(prev => prev.filter(c => c.id !== userId));
    } else {
      setCollaborators(prev =>
        prev.map(c => c.id === userId ? { ...c, role: newRole } : c)
      );
    }
  };

  const handleSendMessage = (message) => {
    const newMessage = {
      id: messages.length + 1,
      userId: currentUser.id,
      userName: currentUser.name,
      avatar: currentUser.avatar,
      content: message,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, newMessage]);
  };

  const handleMentionUser = () => {
    console.log('Opening user mention picker');
  };

  const handleContentChange = (content) => {
    console.log('Document content changed');
    // In real app, sync with other users
  };

  const handleAddComment = (commentData) => {
    console.log('Adding comment:', commentData);
    // In real app, add comment to document
  };

  const handleRestoreVersion = (versionId) => {
    console.log('Restoring version:', versionId);
    // In real app, restore document to selected version
  };

  const handleCompareVersions = (version1, version2) => {
    console.log('Comparing versions:', version1, 'and', version2);
    // In real app, show version comparison view
  };

  const handleCreateTask = (task) => {
    setTasks(prev => [...prev, task]);
  };

  const handleUpdateTask = (taskId, updates) => {
    setTasks(prev =>
      prev.map(task => task.id === taskId ? { ...task, ...updates } : task)
    );
  };

  const handleDeleteTask = (taskId) => {
    setTasks(prev => prev.filter(task => task.id !== taskId));
  };

  const sidebarTabs = [
    { id: 'collaborators', label: 'Team', icon: 'Users', count: collaborators.length },
    { id: 'chat', label: 'Chat', icon: 'MessageCircle', count: messages.length },
    { id: 'history', label: 'History', icon: 'GitBranch', count: versions.length },
    { id: 'tasks', label: 'Tasks', icon: 'CheckSquare', count: tasks.filter(t => t.status !== 'completed').length }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <QuickActionSidebar />
      
      <main className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumbs />
          
          {/* Page Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-2xl font-heading font-bold text-text-primary">
                Collaboration Workspace
              </h1>
              <p className="text-text-secondary mt-1">
                Real-time collaborative editing with your team
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button variant="outline" iconName="Download">
                Export
              </Button>
              <Button variant="secondary" iconName="Eye">
                Preview
              </Button>
              <Button variant="primary" iconName="Share2">
                Share Document
              </Button>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
            {/* Document Editor - Main Area */}
            <div className="xl:col-span-3">
              <DocumentEditor
                document={document}
                collaborators={collaborators}
                currentUser={currentUser}
                onContentChange={handleContentChange}
                onAddComment={handleAddComment}
                comments={comments}
                cursors={cursors}
              />
            </div>

            {/* Right Sidebar */}
            <div className="xl:col-span-1 space-y-6">
              {/* Sidebar Tabs */}
              <div className="bg-surface border border-border rounded-lg">
                <div className="flex items-center border-b border-border">
                  {sidebarTabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex-1 flex items-center justify-center space-x-2 px-3 py-3 text-sm font-medium transition-micro ${
                        activeTab === tab.id
                          ? 'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-text-primary hover:bg-background'
                      }`}
                    >
                      <Icon name={tab.icon} size={16} />
                      <span className="hidden sm:inline">{tab.label}</span>
                      {tab.count > 0 && (
                        <span className="bg-accent text-accent-foreground text-xs px-1.5 py-0.5 rounded-full">
                          {tab.count}
                        </span>
                      )}
                    </button>
                  ))}
                </div>

                {/* Tab Content */}
                <div className="p-0">
                  {activeTab === 'collaborators' && (
                    <CollaboratorPanel
                      collaborators={collaborators}
                      onInviteUser={handleInviteUser}
                      onChangePermission={handleChangePermission}
                    />
                  )}
                  
                  {activeTab === 'chat' && (
                    <ChatPanel
                      messages={messages}
                      currentUser={currentUser}
                      onSendMessage={handleSendMessage}
                      onMentionUser={handleMentionUser}
                    />
                  )}
                  
                  {activeTab === 'history' && (
                    <VersionHistory
                      versions={versions}
                      onRestoreVersion={handleRestoreVersion}
                      onCompareVersions={handleCompareVersions}
                    />
                  )}
                  
                  {activeTab === 'tasks' && (
                    <TaskPanel
                      tasks={tasks}
                      collaborators={collaborators}
                      onCreateTask={handleCreateTask}
                      onUpdateTask={handleUpdateTask}
                      onDeleteTask={handleDeleteTask}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default CollaborationWorkspace;
import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const CollaboratorPanel = ({ collaborators, onInviteUser, onChangePermission }) => {
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('editor');

  const handleInvite = () => {
    if (inviteEmail.trim()) {
      onInviteUser(inviteEmail, inviteRole);
      setInviteEmail('');
      setShowInviteModal(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'text-success';
      case 'away': return 'text-warning';
      case 'offline': return 'text-text-secondary';
      default: return 'text-text-secondary';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'active': return 'Circle';
      case 'away': return 'Clock';
      case 'offline': return 'Minus';
      default: return 'Minus';
    }
  };

  const getRoleColor = (role) => {
    switch (role) {
      case 'owner': return 'bg-primary text-primary-foreground';
      case 'editor': return 'bg-secondary text-secondary-foreground';
      case 'commenter': return 'bg-warning text-warning-foreground';
      case 'viewer': return 'bg-text-secondary text-white';
      default: return 'bg-text-secondary text-white';
    }
  };

  return (
    <div className="bg-surface border border-border rounded-lg">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-text-primary">Collaborators ({collaborators.length})</h3>
          <Button
            variant="primary"
            size="sm"
            iconName="UserPlus"
            onClick={() => setShowInviteModal(true)}
          >
            Invite
          </Button>
        </div>
      </div>

      <div className="p-4 space-y-3 max-h-80 overflow-y-auto">
        {collaborators.map((collaborator) => (
          <div key={collaborator.id} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-background transition-micro">
            <div className="relative">
              <Image
                src={collaborator.avatar}
                alt={collaborator.name}
                className="w-8 h-8 rounded-full object-cover"
              />
              <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-surface flex items-center justify-center ${
                collaborator.status === 'active' ? 'bg-success' : 
                collaborator.status === 'away' ? 'bg-warning' : 'bg-text-secondary'
              }`}>
                <Icon 
                  name={getStatusIcon(collaborator.status)} 
                  size={8} 
                  color="white" 
                />
              </div>
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <p className="text-sm font-medium text-text-primary truncate">{collaborator.name}</p>
                <span className={`text-xs px-2 py-0.5 rounded-full ${getRoleColor(collaborator.role)}`}>
                  {collaborator.role}
                </span>
              </div>
              <p className="text-xs text-text-secondary truncate">{collaborator.email}</p>
              {collaborator.lastSeen && (
                <p className="text-xs text-text-secondary">
                  Last seen: {collaborator.lastSeen}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-1">
              {collaborator.isTyping && (
                <div className="flex items-center space-x-1">
                  <div className="flex space-x-0.5">
                    <div className="w-1 h-1 bg-secondary rounded-full animate-bounce"></div>
                    <div className="w-1 h-1 bg-secondary rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                    <div className="w-1 h-1 bg-secondary rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                  </div>
                </div>
              )}
              
              {collaborator.role !== 'owner' && (
                <div className="relative group">
                  <Button variant="ghost" size="sm" className="p-1">
                    <Icon name="MoreVertical" size={14} />
                  </Button>
                  <div className="absolute right-0 top-full mt-1 w-32 bg-surface border border-border rounded-lg shadow-elevation-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-50">
                    <button
                      onClick={() => onChangePermission(collaborator.id, 'editor')}
                      className="w-full text-left px-3 py-2 text-xs hover:bg-background"
                    >
                      Make Editor
                    </button>
                    <button
                      onClick={() => onChangePermission(collaborator.id, 'commenter')}
                      className="w-full text-left px-3 py-2 text-xs hover:bg-background"
                    >
                      Comment Only
                    </button>
                    <button
                      onClick={() => onChangePermission(collaborator.id, 'viewer')}
                      className="w-full text-left px-3 py-2 text-xs hover:bg-background"
                    >
                      View Only
                    </button>
                    <hr className="border-border" />
                    <button
                      onClick={() => onChangePermission(collaborator.id, 'remove')}
                      className="w-full text-left px-3 py-2 text-xs text-error hover:bg-background"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1000">
          <div className="bg-surface rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text-primary">Invite Collaborator</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowInviteModal(false)}
              >
                <Icon name="X" size={16} />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="Enter email address"
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Permission Level
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value)}
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                >
                  <option value="editor">Editor - Can edit and comment</option>
                  <option value="commenter">Commenter - Can comment only</option>
                  <option value="viewer">Viewer - Can view only</option>
                </select>
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowInviteModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleInvite}
                  className="flex-1"
                >
                  Send Invite
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CollaboratorPanel;
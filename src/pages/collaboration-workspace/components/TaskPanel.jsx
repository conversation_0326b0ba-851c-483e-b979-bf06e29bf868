import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const TaskPanel = ({ tasks, collaborators, onCreateTask, onUpdateTask, onDeleteTask }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    assignee: '',
    priority: 'medium',
    dueDate: '',
    type: 'general'
  });
  const [filter, setFilter] = useState('all');

  const handleCreateTask = () => {
    if (newTask.title.trim()) {
      onCreateTask({
        ...newTask,
        id: Date.now(),
        status: 'pending',
        createdAt: new Date(),
        createdBy: 'current-user'
      });
      setNewTask({
        title: '',
        description: '',
        assignee: '',
        priority: 'medium',
        dueDate: '',
        type: 'general'
      });
      setShowCreateModal(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-success bg-success/10';
      case 'in-progress': return 'text-warning bg-warning/10';
      case 'pending': return 'text-text-secondary bg-background';
      case 'overdue': return 'text-error bg-error/10';
      default: return 'text-text-secondary bg-background';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-error';
      case 'medium': return 'text-warning';
      case 'low': return 'text-success';
      default: return 'text-text-secondary';
    }
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'high': return 'AlertCircle';
      case 'medium': return 'AlertTriangle';
      case 'low': return 'CheckCircle';
      default: return 'Circle';
    }
  };

  const getTaskTypeIcon = (type) => {
    switch (type) {
      case 'review': return 'Eye';
      case 'edit': return 'Edit';
      case 'comment': return 'MessageSquare';
      case 'approval': return 'CheckSquare';
      default: return 'Circle';
    }
  };

  const filteredTasks = tasks.filter(task => {
    if (filter === 'all') return true;
    if (filter === 'my-tasks') return task.assignee === 'current-user';
    if (filter === 'completed') return task.status === 'completed';
    if (filter === 'pending') return task.status === 'pending';
    return true;
  });

  const formatDueDate = (date) => {
    if (!date) return null;
    const dueDate = new Date(date);
    const now = new Date();
    const diffInDays = Math.ceil((dueDate - now) / (1000 * 60 * 60 * 24));
    
    if (diffInDays < 0) return 'Overdue';
    if (diffInDays === 0) return 'Due today';
    if (diffInDays === 1) return 'Due tomorrow';
    return `Due in ${diffInDays} days`;
  };

  return (
    <div className="bg-surface border border-border rounded-lg">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-semibold text-text-primary">Tasks & Assignments</h3>
          <Button
            variant="primary"
            size="sm"
            iconName="Plus"
            onClick={() => setShowCreateModal(true)}
          >
            New Task
          </Button>
        </div>

        {/* Filter Tabs */}
        <div className="flex items-center space-x-1">
          {[
            { key: 'all', label: 'All', count: tasks.length },
            { key: 'my-tasks', label: 'My Tasks', count: tasks.filter(t => t.assignee === 'current-user').length },
            { key: 'pending', label: 'Pending', count: tasks.filter(t => t.status === 'pending').length },
            { key: 'completed', label: 'Done', count: tasks.filter(t => t.status === 'completed').length }
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setFilter(tab.key)}
              className={`px-3 py-1.5 text-xs rounded-lg transition-micro ${
                filter === tab.key
                  ? 'bg-primary text-primary-foreground'
                  : 'text-text-secondary hover:text-text-primary hover:bg-background'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      <div className="max-h-80 overflow-y-auto">
        {filteredTasks.length === 0 ? (
          <div className="p-8 text-center">
            <Icon name="CheckSquare" size={32} className="mx-auto text-text-secondary mb-2" />
            <p className="text-sm text-text-secondary">No tasks found</p>
            <p className="text-xs text-text-secondary">Create a task to get started</p>
          </div>
        ) : (
          <div className="divide-y divide-border">
            {filteredTasks.map((task) => {
              const assignee = collaborators.find(c => c.id === task.assignee);
              return (
                <div key={task.id} className="p-4 hover:bg-background transition-micro">
                  <div className="flex items-start space-x-3">
                    <button
                      onClick={() => onUpdateTask(task.id, { 
                        status: task.status === 'completed' ? 'pending' : 'completed' 
                      })}
                      className={`mt-1 w-4 h-4 rounded border-2 flex items-center justify-center transition-micro ${
                        task.status === 'completed'
                          ? 'bg-success border-success' :'border-border hover:border-primary'
                      }`}
                    >
                      {task.status === 'completed' && (
                        <Icon name="Check" size={10} color="white" />
                      )}
                    </button>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className={`text-sm font-medium ${
                            task.status === 'completed' 
                              ? 'text-text-secondary line-through' :'text-text-primary'
                          }`}>
                            {task.title}
                          </h4>
                          
                          {task.description && (
                            <p className="text-xs text-text-secondary mt-1 line-clamp-2">
                              {task.description}
                            </p>
                          )}

                          <div className="flex items-center space-x-3 mt-2">
                            {/* Priority */}
                            <div className={`flex items-center space-x-1 ${getPriorityColor(task.priority)}`}>
                              <Icon name={getPriorityIcon(task.priority)} size={10} />
                              <span className="text-xs capitalize">{task.priority}</span>
                            </div>

                            {/* Type */}
                            <div className="flex items-center space-x-1 text-text-secondary">
                              <Icon name={getTaskTypeIcon(task.type)} size={10} />
                              <span className="text-xs capitalize">{task.type}</span>
                            </div>

                            {/* Due Date */}
                            {task.dueDate && (
                              <div className={`flex items-center space-x-1 text-xs ${
                                task.status === 'overdue' ? 'text-error' : 'text-text-secondary'
                              }`}>
                                <Icon name="Calendar" size={10} />
                                <span>{formatDueDate(task.dueDate)}</span>
                              </div>
                            )}
                          </div>

                          {/* Assignee */}
                          {assignee && (
                            <div className="flex items-center space-x-2 mt-2">
                              <Image
                                src={assignee.avatar}
                                alt={assignee.name}
                                className="w-5 h-5 rounded-full object-cover"
                              />
                              <span className="text-xs text-text-secondary">
                                Assigned to {assignee.name}
                              </span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-1 ml-2">
                          <span className={`text-xs px-2 py-0.5 rounded-full ${getStatusColor(task.status)}`}>
                            {task.status.replace('-', ' ')}
                          </span>
                          
                          <div className="relative group">
                            <Button variant="ghost" size="sm" className="p-1">
                              <Icon name="MoreVertical" size={12} />
                            </Button>
                            <div className="absolute right-0 top-full mt-1 w-32 bg-surface border border-border rounded-lg shadow-elevation-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all z-50">
                              <button
                                onClick={() => onUpdateTask(task.id, { status: 'in-progress' })}
                                className="w-full text-left px-3 py-2 text-xs hover:bg-background"
                              >
                                Mark In Progress
                              </button>
                              <button
                                onClick={() => onUpdateTask(task.id, { priority: 'high' })}
                                className="w-full text-left px-3 py-2 text-xs hover:bg-background"
                              >
                                Set High Priority
                              </button>
                              <hr className="border-border" />
                              <button
                                onClick={() => onDeleteTask(task.id)}
                                className="w-full text-left px-3 py-2 text-xs text-error hover:bg-background"
                              >
                                Delete Task
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Create Task Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1000">
          <div className="bg-surface rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text-primary">Create New Task</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCreateModal(false)}
              >
                <Icon name="X" size={16} />
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Task Title *
                </label>
                <input
                  type="text"
                  value={newTask.title}
                  onChange={(e) => setNewTask(prev => ({ ...prev, title: e.target.value }))}
                  placeholder="Enter task title"
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Description
                </label>
                <textarea
                  value={newTask.description}
                  onChange={(e) => setNewTask(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Task description (optional)"
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  rows="3"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Assignee
                  </label>
                  <select
                    value={newTask.assignee}
                    onChange={(e) => setNewTask(prev => ({ ...prev, assignee: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Select assignee</option>
                    {collaborators.map((collaborator) => (
                      <option key={collaborator.id} value={collaborator.id}>
                        {collaborator.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Priority
                  </label>
                  <select
                    value={newTask.priority}
                    onChange={(e) => setNewTask(prev => ({ ...prev, priority: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Task Type
                  </label>
                  <select
                    value={newTask.type}
                    onChange={(e) => setNewTask(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="general">General</option>
                    <option value="review">Review</option>
                    <option value="edit">Edit</option>
                    <option value="comment">Comment</option>
                    <option value="approval">Approval</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Due Date
                  </label>
                  <input
                    type="date"
                    value={newTask.dueDate}
                    onChange={(e) => setNewTask(prev => ({ ...prev, dueDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                  />
                </div>
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowCreateModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCreateTask}
                  disabled={!newTask.title.trim()}
                  className="flex-1"
                >
                  Create Task
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskPanel;
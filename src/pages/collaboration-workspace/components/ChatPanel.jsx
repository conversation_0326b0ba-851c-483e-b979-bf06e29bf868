import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const ChatPanel = ({ messages, currentUser, onSendMessage, onMentionUser }) => {
  const [newMessage, setNewMessage] = useState('');
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);

  const emojis = ['👍', '👎', '😊', '😂', '❤️', '🎉', '👏', '🔥', '💡', '✅'];

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      onSendMessage(newMessage.trim());
      setNewMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleEmojiClick = (emoji) => {
    setNewMessage(prev => prev + emoji);
    setShowEmojiPicker(false);
    inputRef.current?.focus();
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now - messageTime) / 60000);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return messageTime.toLocaleDateString();
  };

  const renderMessage = (message) => {
    const isCurrentUser = message.userId === currentUser.id;
    
    return (
      <div key={message.id} className={`flex space-x-3 ${isCurrentUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
        <Image
          src={message.avatar}
          alt={message.userName}
          className="w-6 h-6 rounded-full object-cover flex-shrink-0"
        />
        
        <div className={`flex-1 ${isCurrentUser ? 'text-right' : ''}`}>
          <div className="flex items-center space-x-2 mb-1">
            <span className="text-xs font-medium text-text-primary">{message.userName}</span>
            <span className="text-xs text-text-secondary">{formatTimestamp(message.timestamp)}</span>
          </div>
          
          <div className={`inline-block px-3 py-2 rounded-lg text-sm max-w-xs ${
            isCurrentUser 
              ? 'bg-primary text-primary-foreground' 
              : 'bg-background text-text-primary'
          }`}>
            {message.content}
          </div>
          
          {message.reactions && message.reactions.length > 0 && (
            <div className="flex items-center space-x-1 mt-1">
              {message.reactions.map((reaction, index) => (
                <span key={index} className="text-xs bg-surface border border-border rounded px-1">
                  {reaction.emoji} {reaction.count}
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-surface border border-border rounded-lg flex flex-col h-96">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-text-primary">Team Chat</h3>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-text-secondary">{messages.length} messages</span>
            <Button variant="ghost" size="sm" className="p-1">
              <Icon name="MoreHorizontal" size={14} />
            </Button>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 p-4 overflow-y-auto space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <Icon name="MessageCircle" size={32} className="mx-auto text-text-secondary mb-2" />
            <p className="text-sm text-text-secondary">No messages yet</p>
            <p className="text-xs text-text-secondary">Start a conversation with your team</p>
          </div>
        ) : (
          messages.map(renderMessage)
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div className="p-4 border-t border-border">
        <div className="flex items-end space-x-2">
          <div className="flex-1 relative">
            <textarea
              ref={inputRef}
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Type a message..."
              className="w-full px-3 py-2 pr-10 border border-border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary text-sm"
              rows="2"
            />
            
            <div className="absolute right-2 bottom-2 flex items-center space-x-1">
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                  className="p-1"
                >
                  <Icon name="Smile" size={14} />
                </Button>
                
                {showEmojiPicker && (
                  <div className="absolute bottom-full right-0 mb-2 bg-surface border border-border rounded-lg p-2 shadow-elevation-2 z-50">
                    <div className="grid grid-cols-5 gap-1">
                      {emojis.map((emoji, index) => (
                        <button
                          key={index}
                          onClick={() => handleEmojiClick(emoji)}
                          className="p-1 hover:bg-background rounded text-sm"
                        >
                          {emoji}
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onMentionUser()}
                className="p-1"
              >
                <Icon name="AtSign" size={14} />
              </Button>
            </div>
          </div>
          
          <Button
            variant="primary"
            size="sm"
            onClick={handleSendMessage}
            disabled={!newMessage.trim()}
            iconName="Send"
          >
            Send
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ChatPanel;
import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const DocumentEditor = ({ 
  document, 
  collaborators, 
  currentUser, 
  onContentChange, 
  onAddComment, 
  comments,
  cursors 
}) => {
  const [content, setContent] = useState(document.content || '');
  const [selectedText, setSelectedText] = useState('');
  const [selectionRange, setSelectionRange] = useState(null);
  const [showCommentModal, setShowCommentModal] = useState(false);
  const [commentText, setCommentText] = useState('');
  const [isFormatting, setIsFormatting] = useState(false);
  const editorRef = useRef(null);

  const formatButtons = [
    { name: 'Bold', icon: 'Bold', command: 'bold' },
    { name: 'Italic', icon: 'Italic', command: 'italic' },
    { name: 'Underline', icon: 'Underline', command: 'underline' },
    { name: 'Strikethrough', icon: 'Strikethrough', command: 'strikethrough' },
  ];

  const alignButtons = [
    { name: 'Align Left', icon: 'AlignLeft', command: 'justifyLeft' },
    { name: 'Align Center', icon: 'AlignCenter', command: 'justifyCenter' },
    { name: 'Align Right', icon: 'AlignRight', command: 'justifyRight' },
    { name: 'Justify', icon: 'AlignJustify', command: 'justifyFull' },
  ];

  const listButtons = [
    { name: 'Bullet List', icon: 'List', command: 'insertUnorderedList' },
    { name: 'Numbered List', icon: 'ListOrdered', command: 'insertOrderedList' },
  ];

  useEffect(() => {
    const handleSelectionChange = () => {
      const selection = window.getSelection();
      if (selection.rangeCount > 0 && selection.toString().length > 0) {
        setSelectedText(selection.toString());
        setSelectionRange(selection.getRangeAt(0));
      } else {
        setSelectedText('');
        setSelectionRange(null);
      }
    };

    window.document.addEventListener('selectionchange', handleSelectionChange);
    return () => window.document.removeEventListener('selectionchange', handleSelectionChange);
  }, []);

  const handleContentChange = (e) => {
    const newContent = e.target.innerHTML;
    setContent(newContent);
    onContentChange(newContent);
  };

  const handleFormat = (command) => {
    setIsFormatting(true);
    document.execCommand(command, false, null);
    editorRef.current?.focus();
    setTimeout(() => setIsFormatting(false), 100);
  };

  const handleAddComment = () => {
    if (selectedText && commentText.trim()) {
      onAddComment({
        text: selectedText,
        comment: commentText,
        range: selectionRange,
        position: { x: 0, y: 0 } // In real app, calculate actual position
      });
      setCommentText('');
      setShowCommentModal(false);
      setSelectedText('');
    }
  };

  const renderCursor = (cursor) => (
    <div
      key={cursor.userId}
      className="absolute pointer-events-none z-10"
      style={{
        left: cursor.x,
        top: cursor.y,
        transform: 'translate(-50%, -100%)'
      }}
    >
      <div className="flex items-center space-x-1">
        <div 
          className="w-0.5 h-4 animate-pulse"
          style={{ backgroundColor: cursor.color }}
        />
        <div 
          className="px-2 py-1 rounded text-xs text-white text-nowrap"
          style={{ backgroundColor: cursor.color }}
        >
          {cursor.userName}
        </div>
      </div>
    </div>
  );

  return (
    <div className="bg-surface border border-border rounded-lg flex flex-col h-full">
      {/* Toolbar */}
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <Icon name="FileText" size={20} color="var(--color-primary)" />
            <h2 className="text-lg font-semibold text-text-primary">{document.title}</h2>
            <span className="text-xs text-text-secondary bg-background px-2 py-1 rounded">
              {document.status}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" iconName="Download">
              Export
            </Button>
            <Button variant="primary" size="sm" iconName="Share2">
              Share
            </Button>
          </div>
        </div>

        {/* Formatting Toolbar */}
        <div className="flex items-center space-x-1 flex-wrap gap-2">
          {/* Text Formatting */}
          <div className="flex items-center space-x-1 border-r border-border pr-2">
            {formatButtons.map((button) => (
              <Button
                key={button.command}
                variant="ghost"
                size="sm"
                onClick={() => handleFormat(button.command)}
                className="p-2"
                title={button.name}
              >
                <Icon name={button.icon} size={14} />
              </Button>
            ))}
          </div>

          {/* Alignment */}
          <div className="flex items-center space-x-1 border-r border-border pr-2">
            {alignButtons.map((button) => (
              <Button
                key={button.command}
                variant="ghost"
                size="sm"
                onClick={() => handleFormat(button.command)}
                className="p-2"
                title={button.name}
              >
                <Icon name={button.icon} size={14} />
              </Button>
            ))}
          </div>

          {/* Lists */}
          <div className="flex items-center space-x-1 border-r border-border pr-2">
            {listButtons.map((button) => (
              <Button
                key={button.command}
                variant="ghost"
                size="sm"
                onClick={() => handleFormat(button.command)}
                className="p-2"
                title={button.name}
              >
                <Icon name={button.icon} size={14} />
              </Button>
            ))}
          </div>

          {/* Additional Tools */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => selectedText && setShowCommentModal(true)}
              disabled={!selectedText}
              className="p-2"
              title="Add Comment"
            >
              <Icon name="MessageSquare" size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
              title="Insert Link"
            >
              <Icon name="Link" size={14} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="p-2"
              title="Insert Image"
            >
              <Icon name="Image" size={14} />
            </Button>
          </div>
        </div>
      </div>

      {/* Editor Area */}
      <div className="flex-1 relative">
        {/* Live Cursors */}
        {cursors.map(renderCursor)}

        {/* Editor */}
        <div
          ref={editorRef}
          contentEditable
          onInput={handleContentChange}
          className="w-full h-full p-6 focus:outline-none text-text-primary leading-relaxed"
          style={{ minHeight: '500px' }}
          dangerouslySetInnerHTML={{ __html: content }}
        />

        {/* Comments Overlay */}
        {comments.map((comment, index) => (
          <div
            key={index}
            className="absolute bg-warning/20 border-l-2 border-warning pointer-events-none"
            style={{
              left: comment.position.x,
              top: comment.position.y,
              width: '200px',
              height: '20px'
            }}
          >
            <div className="absolute left-full top-0 ml-2 bg-surface border border-border rounded-lg p-2 shadow-elevation-2 pointer-events-auto z-20">
              <div className="flex items-start space-x-2">
                <div className="w-6 h-6 bg-warning rounded-full flex items-center justify-center">
                  <Icon name="MessageSquare" size={12} color="white" />
                </div>
                <div className="flex-1">
                  <p className="text-xs font-medium text-text-primary">{comment.author}</p>
                  <p className="text-xs text-text-secondary mt-1">{comment.comment}</p>
                  <p className="text-xs text-text-secondary mt-1">"{comment.text}"</p>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Status Bar */}
      <div className="p-3 border-t border-border bg-background">
        <div className="flex items-center justify-between text-xs text-text-secondary">
          <div className="flex items-center space-x-4">
            <span>Words: {content.replace(/<[^>]*>/g, '').split(/\s+/).filter(word => word.length > 0).length}</span>
            <span>Characters: {content.replace(/<[^>]*>/g, '').length}</span>
            <span>Last saved: {document.lastSaved}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            {collaborators.slice(0, 3).map((collaborator) => (
              <div key={collaborator.id} className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-success rounded-full"></div>
                <span>{collaborator.name}</span>
              </div>
            ))}
            {collaborators.length > 3 && (
              <span>+{collaborators.length - 3} more</span>
            )}
          </div>
        </div>
      </div>

      {/* Comment Modal */}
      {showCommentModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1000">
          <div className="bg-surface rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text-primary">Add Comment</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowCommentModal(false)}
              >
                <Icon name="X" size={16} />
              </Button>
            </div>

            <div className="space-y-4">
              <div className="p-3 bg-background rounded-lg">
                <p className="text-sm text-text-secondary">Selected text:</p>
                <p className="text-sm text-text-primary font-medium">"{selectedText}"</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">
                  Your Comment
                </label>
                <textarea
                  value={commentText}
                  onChange={(e) => setCommentText(e.target.value)}
                  placeholder="Add your comment..."
                  className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  rows="3"
                />
              </div>

              <div className="flex space-x-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowCommentModal(false)}
                  className="flex-1"
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={handleAddComment}
                  disabled={!commentText.trim()}
                  className="flex-1"
                >
                  Add Comment
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentEditor;
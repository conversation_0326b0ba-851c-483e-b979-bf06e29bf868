import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const VersionHistory = ({ versions, onRestoreVersion, onCompareVersions }) => {
  const [selectedVersions, setSelectedVersions] = useState([]);
  const [expandedVersion, setExpandedVersion] = useState(null);

  const handleVersionSelect = (versionId) => {
    setSelectedVersions(prev => {
      if (prev.includes(versionId)) {
        return prev.filter(id => id !== versionId);
      } else if (prev.length < 2) {
        return [...prev, versionId];
      } else {
        return [prev[1], versionId];
      }
    });
  };

  const handleCompare = () => {
    if (selectedVersions.length === 2) {
      onCompareVersions(selectedVersions[0], selectedVersions[1]);
    }
  };

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now - date) / 3600000);
    
    if (diffInHours < 1) return 'Less than an hour ago';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)} days ago`;
    return date.toLocaleDateString();
  };

  const getChangeTypeColor = (type) => {
    switch (type) {
      case 'major': return 'text-error';
      case 'minor': return 'text-warning';
      case 'patch': return 'text-success';
      default: return 'text-text-secondary';
    }
  };

  const getChangeTypeIcon = (type) => {
    switch (type) {
      case 'major': return 'AlertCircle';
      case 'minor': return 'AlertTriangle';
      case 'patch': return 'CheckCircle';
      default: return 'Circle';
    }
  };

  return (
    <div className="bg-surface border border-border rounded-lg">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-semibold text-text-primary">Version History</h3>
          <div className="flex items-center space-x-2">
            {selectedVersions.length === 2 && (
              <Button
                variant="secondary"
                size="sm"
                onClick={handleCompare}
                iconName="GitCompare"
              >
                Compare
              </Button>
            )}
            <Button variant="ghost" size="sm" className="p-1">
              <Icon name="MoreHorizontal" size={14} />
            </Button>
          </div>
        </div>
        
        {selectedVersions.length > 0 && (
          <p className="text-xs text-text-secondary mt-2">
            {selectedVersions.length === 1 
              ? '1 version selected' 
              : `${selectedVersions.length} versions selected for comparison`
            }
          </p>
        )}
      </div>

      <div className="max-h-96 overflow-y-auto">
        {versions.map((version, index) => (
          <div key={version.id} className="border-b border-border last:border-b-0">
            <div className="p-4 hover:bg-background transition-micro">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <input
                    type="checkbox"
                    checked={selectedVersions.includes(version.id)}
                    onChange={() => handleVersionSelect(version.id)}
                    className="w-4 h-4 text-primary border-border rounded focus:ring-primary"
                  />
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <h4 className="text-sm font-medium text-text-primary">
                        Version {version.version}
                      </h4>
                      <span className={`inline-flex items-center space-x-1 text-xs ${getChangeTypeColor(version.changeType)}`}>
                        <Icon name={getChangeTypeIcon(version.changeType)} size={12} />
                        <span className="capitalize">{version.changeType}</span>
                      </span>
                      {index === 0 && (
                        <span className="bg-primary text-primary-foreground text-xs px-2 py-0.5 rounded-full">
                          Current
                        </span>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setExpandedVersion(expandedVersion === version.id ? null : version.id)}
                        className="p-1"
                      >
                        <Icon name={expandedVersion === version.id ? "ChevronUp" : "ChevronDown"} size={14} />
                      </Button>
                      
                      {index !== 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => onRestoreVersion(version.id)}
                          className="p-1"
                          title="Restore this version"
                        >
                          <Icon name="RotateCcw" size={14} />
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center space-x-2 mt-1">
                    <Image
                      src={version.author.avatar}
                      alt={version.author.name}
                      className="w-5 h-5 rounded-full object-cover"
                    />
                    <span className="text-xs text-text-secondary">
                      {version.author.name}
                    </span>
                    <span className="text-xs text-text-secondary">•</span>
                    <span className="text-xs text-text-secondary">
                      {formatTimestamp(version.timestamp)}
                    </span>
                  </div>

                  <p className="text-xs text-text-secondary mt-1">
                    {version.summary}
                  </p>

                  <div className="flex items-center space-x-4 mt-2 text-xs text-text-secondary">
                    <span className="flex items-center space-x-1">
                      <Icon name="Plus" size={10} color="var(--color-success)" />
                      <span>{version.changes.additions} additions</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Icon name="Minus" size={10} color="var(--color-error)" />
                      <span>{version.changes.deletions} deletions</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Icon name="Edit" size={10} color="var(--color-warning)" />
                      <span>{version.changes.modifications} modifications</span>
                    </span>
                  </div>

                  {/* Expanded Details */}
                  {expandedVersion === version.id && (
                    <div className="mt-3 pt-3 border-t border-border">
                      <div className="space-y-2">
                        <h5 className="text-xs font-medium text-text-primary">Changes in this version:</h5>
                        {version.detailedChanges.map((change, changeIndex) => (
                          <div key={changeIndex} className="flex items-start space-x-2 text-xs">
                            <Icon 
                              name={change.type === 'addition' ? 'Plus' : change.type === 'deletion' ? 'Minus' : 'Edit'} 
                              size={10} 
                              color={
                                change.type === 'addition' ? 'var(--color-success)' :
                                change.type === 'deletion' ? 'var(--color-error)' :
                                'var(--color-warning)'
                              }
                            />
                            <span className="text-text-secondary">{change.description}</span>
                          </div>
                        ))}
                      </div>

                      {version.comments && version.comments.length > 0 && (
                        <div className="mt-3 pt-3 border-t border-border">
                          <h5 className="text-xs font-medium text-text-primary mb-2">Comments:</h5>
                          {version.comments.map((comment, commentIndex) => (
                            <div key={commentIndex} className="flex items-start space-x-2 text-xs mb-2">
                              <Image
                                src={comment.author.avatar}
                                alt={comment.author.name}
                                className="w-4 h-4 rounded-full object-cover"
                              />
                              <div>
                                <span className="font-medium text-text-primary">{comment.author.name}:</span>
                                <span className="text-text-secondary ml-1">{comment.text}</span>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {versions.length === 0 && (
        <div className="p-8 text-center">
          <Icon name="GitBranch" size={32} className="mx-auto text-text-secondary mb-2" />
          <p className="text-sm text-text-secondary">No version history available</p>
          <p className="text-xs text-text-secondary">Changes will appear here as you edit</p>
        </div>
      )}
    </div>
  );
};

export default VersionHistory;
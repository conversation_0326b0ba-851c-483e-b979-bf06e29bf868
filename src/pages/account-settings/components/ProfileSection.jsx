import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const ProfileSection = () => {
  const [profileData, setProfileData] = useState({
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "+234 ************",
    institution: "University of Lagos",
    userType: "student",
    bio: "PhD candidate in Computer Science with focus on AI and machine learning applications in document processing.",
    profilePhoto: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
  });

  const [isEditing, setIsEditing] = useState(false);
  const [tempData, setTempData] = useState(profileData);

  const userTypes = [
    { value: "student", label: "Student" },
    { value: "educator", label: "Educator" },
    { value: "researcher", label: "Researcher" },
    { value: "business", label: "Business Professional" },
    { value: "entrepreneur", label: "Entrepreneur" },
    { value: "content_creator", label: "Content Creator" }
  ];

  const handleEdit = () => {
    setIsEditing(true);
    setTempData(profileData);
  };

  const handleSave = () => {
    setProfileData(tempData);
    setIsEditing(false);
    console.log('Profile updated:', tempData);
  };

  const handleCancel = () => {
    setTempData(profileData);
    setIsEditing(false);
  };

  const handleInputChange = (field, value) => {
    setTempData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePhotoUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        handleInputChange('profilePhoto', e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-text-primary">Profile Information</h2>
          <p className="text-sm text-text-secondary">Manage your personal information and preferences</p>
        </div>
        {!isEditing ? (
          <Button variant="outline" onClick={handleEdit} iconName="Edit2" iconPosition="left">
            Edit Profile
          </Button>
        ) : (
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={handleCancel}>
              Cancel
            </Button>
            <Button variant="primary" onClick={handleSave} iconName="Save" iconPosition="left">
              Save Changes
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Profile Photo Section */}
        <div className="lg:col-span-1">
          <div className="text-center">
            <div className="relative inline-block">
              <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-border bg-background">
                <Image
                  src={isEditing ? tempData.profilePhoto : profileData.profilePhoto}
                  alt="Profile Photo"
                  className="w-full h-full object-cover"
                />
              </div>
              {isEditing && (
                <label className="absolute bottom-0 right-0 w-10 h-10 bg-primary text-primary-foreground rounded-full flex items-center justify-center cursor-pointer hover:bg-primary/90 transition-micro">
                  <Icon name="Camera" size={16} />
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handlePhotoUpload}
                    className="hidden"
                  />
                </label>
              )}
            </div>
            <div className="mt-4">
              <h3 className="text-lg font-medium text-text-primary">
                {profileData.firstName} {profileData.lastName}
              </h3>
              <p className="text-sm text-text-secondary capitalize">{profileData.userType.replace('_', ' ')}</p>
            </div>
          </div>
        </div>

        {/* Profile Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Basic Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">First Name</label>
                {isEditing ? (
                  <Input
                    type="text"
                    value={tempData.firstName}
                    onChange={(e) => handleInputChange('firstName', e.target.value)}
                    placeholder="Enter first name"
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.firstName}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Last Name</label>
                {isEditing ? (
                  <Input
                    type="text"
                    value={tempData.lastName}
                    onChange={(e) => handleInputChange('lastName', e.target.value)}
                    placeholder="Enter last name"
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.lastName}</p>
                )}
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Email Address</label>
                {isEditing ? (
                  <Input
                    type="email"
                    value={tempData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    placeholder="Enter email address"
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.email}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Phone Number</label>
                {isEditing ? (
                  <Input
                    type="tel"
                    value={tempData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    placeholder="Enter phone number"
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.phone}</p>
                )}
              </div>
            </div>
          </div>

          {/* Professional Information */}
          <div>
            <h4 className="text-md font-medium text-text-primary mb-4">Professional Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">Institution/Organization</label>
                {isEditing ? (
                  <Input
                    type="text"
                    value={tempData.institution}
                    onChange={(e) => handleInputChange('institution', e.target.value)}
                    placeholder="Enter institution name"
                  />
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.institution}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-text-primary mb-2">User Type</label>
                {isEditing ? (
                  <select
                    value={tempData.userType}
                    onChange={(e) => handleInputChange('userType', e.target.value)}
                    className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    {userTypes.map(type => (
                      <option key={type.value} value={type.value}>{type.label}</option>
                    ))}
                  </select>
                ) : (
                  <p className="text-sm text-text-secondary bg-background p-3 rounded-lg capitalize">
                    {userTypes.find(type => type.value === profileData.userType)?.label}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Bio Section */}
          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">Bio</label>
            {isEditing ? (
              <textarea
                value={tempData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                placeholder="Tell us about yourself..."
                rows={4}
                className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text-primary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
              />
            ) : (
              <p className="text-sm text-text-secondary bg-background p-3 rounded-lg">{profileData.bio}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileSection;
import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const SecuritySection = () => {
  const [showPasswordForm, setShowPasswordForm] = useState(false);
  const [showTwoFactorSetup, setShowTwoFactorSetup] = useState(false);
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [showDeleteAccount, setShowDeleteAccount] = useState(false);

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });

  const [activeSessions] = useState([
    {
      id: 1,
      device: "Chrome on Windows",
      location: "Lagos, Nigeria",
      ipAddress: "197.210.xxx.xxx",
      lastActive: "2 minutes ago",
      isCurrent: true
    },
    {
      id: 2,
      device: "Safari on iPhone",
      location: "Lagos, Nigeria",
      ipAddress: "197.210.xxx.xxx",
      lastActive: "1 hour ago",
      isCurrent: false
    },
    {
      id: 3,
      device: "Chrome on Android",
      location: "Abuja, Nigeria",
      ipAddress: "105.112.xxx.xxx",
      lastActive: "2 days ago",
      isCurrent: false
    }
  ]);

  const [loginHistory] = useState([
    {
      id: 1,
      timestamp: "2024-01-15 14:30:25",
      device: "Chrome on Windows",
      location: "Lagos, Nigeria",
      status: "success",
      ipAddress: "197.210.xxx.xxx"
    },
    {
      id: 2,
      timestamp: "2024-01-15 09:15:10",
      device: "Safari on iPhone",
      location: "Lagos, Nigeria",
      status: "success",
      ipAddress: "197.210.xxx.xxx"
    },
    {
      id: 3,
      timestamp: "2024-01-14 16:45:33",
      device: "Chrome on Android",
      location: "Abuja, Nigeria",
      status: "failed",
      ipAddress: "105.112.xxx.xxx"
    }
  ]);

  const handlePasswordChange = (field, value) => {
    setPasswordForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePasswordSubmit = (e) => {
    e.preventDefault();
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      alert('Passwords do not match');
      return;
    }
    console.log('Changing password...');
    setShowPasswordForm(false);
    setPasswordForm({ currentPassword: '', newPassword: '', confirmPassword: '' });
  };

  const handleEnableTwoFactor = () => {
    setTwoFactorEnabled(true);
    setShowTwoFactorSetup(false);
    console.log('Two-factor authentication enabled');
  };

  const handleDisableTwoFactor = () => {
    setTwoFactorEnabled(false);
    console.log('Two-factor authentication disabled');
  };

  const handleTerminateSession = (sessionId) => {
    console.log('Terminating session:', sessionId);
  };

  const handleDeleteAccount = () => {
    console.log('Account deletion requested');
    setShowDeleteAccount(false);
  };

  const getDeviceIcon = (device) => {
    if (device.includes('iPhone') || device.includes('Android')) return 'Smartphone';
    if (device.includes('iPad') || device.includes('Tablet')) return 'Tablet';
    return 'Monitor';
  };

  return (
    <div className="space-y-6">
      {/* Password Security */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">Password Security</h3>
            <p className="text-sm text-text-secondary">Manage your account password and security settings</p>
          </div>
          <Button variant="outline" onClick={() => setShowPasswordForm(true)} iconName="Key" iconPosition="left">
            Change Password
          </Button>
        </div>

        {showPasswordForm && (
          <form onSubmit={handlePasswordSubmit} className="space-y-4 p-4 bg-background rounded-lg">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Current Password</label>
              <Input
                type="password"
                value={passwordForm.currentPassword}
                onChange={(e) => handlePasswordChange('currentPassword', e.target.value)}
                placeholder="Enter current password"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">New Password</label>
              <Input
                type="password"
                value={passwordForm.newPassword}
                onChange={(e) => handlePasswordChange('newPassword', e.target.value)}
                placeholder="Enter new password"
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">Confirm New Password</label>
              <Input
                type="password"
                value={passwordForm.confirmPassword}
                onChange={(e) => handlePasswordChange('confirmPassword', e.target.value)}
                placeholder="Confirm new password"
                required
              />
            </div>
            <div className="flex space-x-2">
              <Button type="submit" variant="primary">Update Password</Button>
              <Button type="button" variant="ghost" onClick={() => setShowPasswordForm(false)}>Cancel</Button>
            </div>
          </form>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-6">
          <div className="p-4 border border-border rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Shield" size={16} color="var(--color-success)" />
              <span className="text-sm font-medium text-text-primary">Password Strength</span>
            </div>
            <p className="text-xs text-text-secondary">Your password meets security requirements</p>
          </div>
          <div className="p-4 border border-border rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="Clock" size={16} color="var(--color-secondary)" />
              <span className="text-sm font-medium text-text-primary">Last Changed</span>
            </div>
            <p className="text-xs text-text-secondary">45 days ago</p>
          </div>
        </div>
      </div>

      {/* Two-Factor Authentication */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-lg font-semibold text-text-primary">Two-Factor Authentication</h3>
            <p className="text-sm text-text-secondary">Add an extra layer of security to your account</p>
          </div>
          <div className="flex items-center space-x-3">
            <span className={`text-sm ${twoFactorEnabled ? 'text-success' : 'text-text-secondary'}`}>
              {twoFactorEnabled ? 'Enabled' : 'Disabled'}
            </span>
            {twoFactorEnabled ? (
              <Button variant="outline" onClick={handleDisableTwoFactor}>
                Disable
              </Button>
            ) : (
              <Button variant="primary" onClick={() => setShowTwoFactorSetup(true)}>
                Enable 2FA
              </Button>
            )}
          </div>
        </div>

        {twoFactorEnabled ? (
          <div className="p-4 bg-success/10 border border-success/20 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="CheckCircle" size={16} color="var(--color-success)" />
              <span className="text-sm font-medium text-success">Two-Factor Authentication is Active</span>
            </div>
            <p className="text-xs text-text-secondary">Your account is protected with 2FA using an authenticator app</p>
          </div>
        ) : (
          <div className="p-4 bg-warning/10 border border-warning/20 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Icon name="AlertTriangle" size={16} color="var(--color-warning)" />
              <span className="text-sm font-medium text-warning">Two-Factor Authentication is Disabled</span>
            </div>
            <p className="text-xs text-text-secondary">Enable 2FA to add an extra layer of security to your account</p>
          </div>
        )}

        {/* 2FA Setup Modal */}
        {showTwoFactorSetup && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
            <div className="bg-surface rounded-lg border border-border max-w-md w-full">
              <div className="p-6 border-b border-border">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-text-primary">Enable Two-Factor Authentication</h4>
                  <Button variant="ghost" onClick={() => setShowTwoFactorSetup(false)}>
                    <Icon name="X" size={20} />
                  </Button>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <div className="text-center">
                  <div className="w-32 h-32 bg-background border-2 border-dashed border-border rounded-lg flex items-center justify-center mx-auto mb-4">
                    <Icon name="QrCode" size={48} color="var(--color-text-secondary)" />
                  </div>
                  <p className="text-sm text-text-secondary mb-4">
                    Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">Verification Code</label>
                  <Input
                    type="text"
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                  />
                </div>
                <div className="flex space-x-2">
                  <Button variant="primary" onClick={handleEnableTwoFactor} className="flex-1">
                    Enable 2FA
                  </Button>
                  <Button variant="ghost" onClick={() => setShowTwoFactorSetup(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Active Sessions */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Active Sessions</h3>
          <p className="text-sm text-text-secondary">Manage devices that are currently signed in to your account</p>
        </div>

        <div className="space-y-3">
          {activeSessions.map((session) => (
            <div key={session.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-secondary/10 rounded-lg flex items-center justify-center">
                  <Icon name={getDeviceIcon(session.device)} size={16} color="var(--color-secondary)" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">{session.device}</span>
                    {session.isCurrent && (
                      <span className="text-xs bg-success text-success-foreground px-2 py-0.5 rounded-full">
                        Current
                      </span>
                    )}
                  </div>
                  <p className="text-xs text-text-secondary">
                    {session.location} • {session.ipAddress} • {session.lastActive}
                  </p>
                </div>
              </div>
              {!session.isCurrent && (
                <Button
                  variant="outline"
                  onClick={() => handleTerminateSession(session.id)}
                  iconName="LogOut"
                  iconPosition="left"
                >
                  Terminate
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Login History */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Login History</h3>
          <p className="text-sm text-text-secondary">Recent login attempts and activities</p>
        </div>

        <div className="space-y-3">
          {loginHistory.map((login) => (
            <div key={login.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
              <div className="flex items-center space-x-3">
                <div className={`w-2 h-2 rounded-full ${
                  login.status === 'success' ? 'bg-success' : 'bg-error'
                }`}></div>
                <div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-text-primary">{login.device}</span>
                    <span className={`text-xs px-2 py-0.5 rounded-full ${
                      login.status === 'success' ?'bg-success/10 text-success' :'bg-error/10 text-error'
                    }`}>
                      {login.status}
                    </span>
                  </div>
                  <p className="text-xs text-text-secondary">
                    {login.timestamp} • {login.location} • {login.ipAddress}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Danger Zone */}
      <div className="bg-surface rounded-lg border border-error p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-error mb-2">Danger Zone</h3>
          <p className="text-sm text-text-secondary">Irreversible and destructive actions</p>
        </div>

        <div className="flex items-center justify-between p-4 bg-error/5 rounded-lg">
          <div>
            <h4 className="text-sm font-medium text-text-primary">Delete Account</h4>
            <p className="text-xs text-text-secondary">Permanently delete your account and all associated data</p>
          </div>
          <Button variant="danger" onClick={() => setShowDeleteAccount(true)}>
            Delete Account
          </Button>
        </div>

        {/* Delete Account Modal */}
        {showDeleteAccount && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
            <div className="bg-surface rounded-lg border border-border max-w-md w-full">
              <div className="p-6 border-b border-border">
                <div className="flex items-center space-x-2">
                  <Icon name="AlertTriangle" size={20} color="var(--color-error)" />
                  <h4 className="text-lg font-semibold text-error">Delete Account</h4>
                </div>
              </div>
              <div className="p-6 space-y-4">
                <p className="text-sm text-text-secondary">
                  This action cannot be undone. This will permanently delete your account and remove all your data from our servers.
                </p>
                <div>
                  <label className="block text-sm font-medium text-text-primary mb-2">
                    Type "DELETE" to confirm
                  </label>
                  <Input
                    type="text"
                    placeholder="DELETE"
                  />
                </div>
                <div className="flex space-x-2">
                  <Button variant="danger" onClick={handleDeleteAccount} className="flex-1">
                    Delete Account
                  </Button>
                  <Button variant="ghost" onClick={() => setShowDeleteAccount(false)} className="flex-1">
                    Cancel
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SecuritySection;
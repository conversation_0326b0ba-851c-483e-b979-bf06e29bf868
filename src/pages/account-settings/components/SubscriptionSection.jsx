import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const SubscriptionSection = () => {
  const [currentPlan] = useState({
    name: "Professional",
    price: "₦15,000",
    period: "monthly",
    credits: 1000,
    usedCredits: 750,
    features: [
      "Unlimited document creation",
      "Advanced AI features",
      "Priority support",
      "Team collaboration",
      "Export to all formats"
    ],
    nextBilling: "2024-02-15"
  });

  const [showUpgradeModal, setShowUpgradeModal] = useState(false);

  const plans = [
    {
      name: "Basic",
      price: "₦5,000",
      period: "monthly",
      credits: 200,
      features: [
        "Basic document creation",
        "Standard templates",
        "PDF export",
        "Email support"
      ],
      popular: false
    },
    {
      name: "Professional",
      price: "₦15,000",
      period: "monthly",
      credits: 1000,
      features: [
        "Unlimited document creation",
        "Advanced AI features",
        "Priority support",
        "Team collaboration",
        "Export to all formats"
      ],
      popular: true
    },
    {
      name: "Enterprise",
      price: "₦35,000",
      period: "monthly",
      credits: 5000,
      features: [
        "Everything in Professional",
        "Custom branding",
        "API access",
        "Dedicated support",
        "Advanced analytics"
      ],
      popular: false
    }
  ];

  const creditPackages = [
    { credits: 100, price: "₦2,000", bonus: 0 },
    { credits: 500, price: "₦8,000", bonus: 50 },
    { credits: 1000, price: "₦15,000", bonus: 150 },
    { credits: 2500, price: "₦35,000", bonus: 500 }
  ];

  const usageStats = [
    { label: "Documents Created", value: 45, icon: "FileText" },
    { label: "Templates Used", value: 12, icon: "Library" },
    { label: "Plagiarism Checks", value: 23, icon: "Shield" },
    { label: "Collaborations", value: 8, icon: "Users" }
  ];

  const creditPercentage = ((currentPlan.credits - currentPlan.usedCredits) / currentPlan.credits) * 100;

  const handleUpgrade = (planName) => {
    console.log('Upgrading to:', planName);
    setShowUpgradeModal(false);
  };

  const handlePurchaseCredits = (packageInfo) => {
    console.log('Purchasing credits:', packageInfo);
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Overview */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-lg font-semibold text-text-primary">Current Subscription</h2>
            <p className="text-sm text-text-secondary">Manage your plan and billing</p>
          </div>
          <Button variant="outline" onClick={() => setShowUpgradeModal(true)} iconName="ArrowUp" iconPosition="left">
            Upgrade Plan
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Plan Details */}
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                <Icon name="Crown" size={24} color="var(--color-primary)" />
              </div>
              <div>
                <h3 className="text-xl font-semibold text-text-primary">{currentPlan.name} Plan</h3>
                <p className="text-sm text-text-secondary">
                  {currentPlan.price}/{currentPlan.period} • Next billing: {currentPlan.nextBilling}
                </p>
              </div>
            </div>

            <div className="space-y-2">
              {currentPlan.features.map((feature, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <Icon name="Check" size={16} color="var(--color-success)" />
                  <span className="text-sm text-text-secondary">{feature}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Credit Usage */}
          <div className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-text-primary">Credit Usage</span>
                <span className="text-sm text-text-secondary">
                  {currentPlan.usedCredits} / {currentPlan.credits} used
                </span>
              </div>
              <div className="w-full bg-background rounded-full h-3">
                <div
                  className="bg-primary h-3 rounded-full transition-all duration-300"
                  style={{ width: `${100 - creditPercentage}%` }}
                ></div>
              </div>
              <p className="text-xs text-text-secondary mt-1">
                {currentPlan.credits - currentPlan.usedCredits} credits remaining
              </p>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {usageStats.map((stat, index) => (
                <div key={index} className="bg-background p-3 rounded-lg">
                  <div className="flex items-center space-x-2 mb-1">
                    <Icon name={stat.icon} size={14} color="var(--color-secondary)" />
                    <span className="text-xs text-text-secondary">{stat.label}</span>
                  </div>
                  <p className="text-lg font-semibold text-text-primary">{stat.value}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Credit Purchase */}
      <div className="bg-surface rounded-lg border border-border p-6">
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-text-primary mb-2">Purchase Additional Credits</h3>
          <p className="text-sm text-text-secondary">Top up your account with extra credits for AI features</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {creditPackages.map((pkg, index) => (
            <div key={index} className="border border-border rounded-lg p-4 hover:border-primary transition-micro">
              <div className="text-center">
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Icon name="Zap" size={20} color="var(--color-accent)" />
                </div>
                <h4 className="text-lg font-semibold text-text-primary">{pkg.credits} Credits</h4>
                {pkg.bonus > 0 && (
                  <p className="text-xs text-success mb-2">+{pkg.bonus} bonus credits</p>
                )}
                <p className="text-xl font-bold text-primary mb-4">{pkg.price}</p>
                <Button
                  variant="outline"
                  onClick={() => handlePurchaseCredits(pkg)}
                  className="w-full"
                >
                  Purchase
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Upgrade Modal */}
      {showUpgradeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-1200 p-4">
          <div className="bg-surface rounded-lg border border-border max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-border">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold text-text-primary">Choose Your Plan</h3>
                  <p className="text-sm text-text-secondary">Select the plan that best fits your needs</p>
                </div>
                <Button variant="ghost" onClick={() => setShowUpgradeModal(false)}>
                  <Icon name="X" size={20} />
                </Button>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.map((plan, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-6 relative ${
                      plan.popular ? 'border-primary bg-primary/5' : 'border-border'
                    }`}
                  >
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <span className="bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium">
                          Most Popular
                        </span>
                      </div>
                    )}

                    <div className="text-center mb-6">
                      <h4 className="text-xl font-semibold text-text-primary mb-2">{plan.name}</h4>
                      <div className="mb-4">
                        <span className="text-3xl font-bold text-primary">{plan.price}</span>
                        <span className="text-text-secondary">/{plan.period}</span>
                      </div>
                      <p className="text-sm text-text-secondary">{plan.credits} credits included</p>
                    </div>

                    <div className="space-y-3 mb-6">
                      {plan.features.map((feature, featureIndex) => (
                        <div key={featureIndex} className="flex items-center space-x-2">
                          <Icon name="Check" size={16} color="var(--color-success)" />
                          <span className="text-sm text-text-secondary">{feature}</span>
                        </div>
                      ))}
                    </div>

                    <Button
                      variant={plan.popular ? "primary" : "outline"}
                      onClick={() => handleUpgrade(plan.name)}
                      className="w-full"
                    >
                      {currentPlan.name === plan.name ? 'Current Plan' : 'Upgrade'}
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionSection;
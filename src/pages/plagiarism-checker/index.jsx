import React, { useState } from 'react';
import { useSidebar } from '../../contexts/SidebarContext';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import StatusNotification from '../../components/ui/StatusNotification';
import UploadArea from './components/UploadArea';
import ProcessingStatus from './components/ProcessingStatus';
import ResultsDashboard from './components/ResultsDashboard';
import DocumentViewer from './components/DocumentViewer';
import RecentChecks from './components/RecentChecks';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';

const PlagiarismChecker = () => {
  const { contentMargin } = useSidebar();
  const [currentStep, setCurrentStep] = useState('upload'); // upload, processing, results
  const [processingData, setProcessingData] = useState({
    progress: 0,
    stage: 'upload',
    estimatedTime: 180,
    creditsUsed: 5
  });
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [results, setResults] = useState(null);

  // Mock results data
  const mockResults = {
    documentName: "Research Paper - Climate Change.docx",
    documentSize: "2.4 MB",
    wordCount: 3250,
    characterCount: 18750,
    processingTime: "2m 34s",
    completedAt: new Date(),
    plagiarism: {
      similarityPercentage: 23,
      webSources: 8,
      academicSources: 5,
      documentSources: 2,
      topSources: [
        {
          title: "Climate Change and Global Warming - Scientific Review",
          url: "https://example.com/climate-research",
          similarity: 15
        },
        {
          title: "Environmental Impact Assessment Report 2023",
          url: "https://example.com/environmental-report",
          similarity: 12
        },
        {
          title: "IPCC Climate Change Report - Chapter 3",
          url: "https://example.com/ipcc-report",
          similarity: 8
        },
        {
          title: "Sustainable Development and Climate Action",
          url: "https://example.com/sustainable-development",
          similarity: 6
        }
      ]
    },
    aiDetection: {
      confidence: 35,
      analysis: [
        {
          metric: "Writing Pattern Analysis",
          score: 28,
          description: "Text shows natural variation in sentence structure and vocabulary"
        },
        {
          metric: "Semantic Consistency",
          score: 42,
          description: "Some sections show consistent semantic patterns typical of AI"
        },
        {
          metric: "Stylistic Fingerprinting",
          score: 31,
          description: "Writing style appears mostly human with some AI-like characteristics"
        },
        {
          metric: "Perplexity Analysis",
          score: 38,
          description: "Text complexity suggests mixed human-AI authorship"
        }
      ]
    }
  };

  // Mock document data for viewer
  const mockDocument = {
    name: "Research Paper - Climate Change.docx",
    content: `Climate change represents one of the most pressing challenges of our time, with far-reaching implications for global ecosystems, human societies, and economic systems.\n\nThe scientific consensus on anthropogenic climate change has strengthened considerably over the past decades, with overwhelming evidence pointing to human activities as the primary driver of observed warming trends.\n\nGreenhouse gas emissions, particularly carbon dioxide from fossil fuel combustion, have increased dramatically since the Industrial Revolution, leading to unprecedented atmospheric concentrations.\n\nThe impacts of climate change are already visible across multiple domains, including rising sea levels, changing precipitation patterns, and increased frequency of extreme weather events.\n\nMitigation strategies must be implemented urgently to limit global temperature rise and prevent the most catastrophic consequences of climate change.`,
    sections: [
      { title: "Introduction", matchCount: 2 },
      { title: "Literature Review", matchCount: 5 },
      { title: "Methodology", matchCount: 1 },
      { title: "Results", matchCount: 3 },
      { title: "Discussion", matchCount: 4 },
      { title: "Conclusion", matchCount: 1 }
    ]
  };

  // Mock matches data
  const mockMatches = [
    {
      paragraphIndex: 0,
      text: "Climate change represents one of the most pressing challenges of our time",
      similarity: 85,
      source: "Climate Change and Global Warming - Scientific Review",
      originalText: "Climate change represents one of the most pressing challenges of our time, with far-reaching implications for global ecosystems, human societies, and economic systems.",
      sourceText: "Climate change represents one of the most pressing challenges of our time, affecting ecosystems, societies, and economies worldwide.",
      suggestion: "Global climate change poses significant challenges to contemporary society, with widespread effects on environmental systems, human communities, and economic structures."
    },
    {
      paragraphIndex: 1,
      text: "scientific consensus on anthropogenic climate change",
      similarity: 72,
      source: "IPCC Climate Change Report - Chapter 3",
      originalText: "The scientific consensus on anthropogenic climate change has strengthened considerably over the past decades",
      sourceText: "There is strong scientific consensus on anthropogenic climate change that has grown over recent decades",
      suggestion: "The scientific agreement regarding human-caused climate change has become increasingly robust in recent years"
    }
  ];

  const handleFileUpload = (file) => {
    console.log('File uploaded:', file.name);
    setSelectedDocument(file);
    startProcessing();
  };

  const handleTextSubmit = (text) => {
    console.log('Text submitted:', text.substring(0, 100) + '...');
    setSelectedDocument({ name: 'Pasted Text', content: text });
    startProcessing();
  };

  const startProcessing = () => {
    setCurrentStep('processing');
    
    // Simulate processing stages
    const stages = ['upload', 'analysis', 'scanning', 'ai-detection', 'report'];
    let currentStageIndex = 0;
    let progress = 0;
    
    const interval = setInterval(() => {
      progress += Math.random() * 15 + 5;
      
      if (progress >= 100) {
        progress = 100;
        setProcessingData(prev => ({
          ...prev,
          progress: 100,
          stage: 'report'
        }));
        
        setTimeout(() => {
          setResults(mockResults);
          setCurrentStep('results');
        }, 1000);
        
        clearInterval(interval);
        return;
      }
      
      // Update stage based on progress
      const stageProgress = progress / 20;
      if (Math.floor(stageProgress) > currentStageIndex && currentStageIndex < stages.length - 1) {
        currentStageIndex++;
      }
      
      setProcessingData(prev => ({
        ...prev,
        progress: Math.min(progress, 100),
        stage: stages[currentStageIndex],
        estimatedTime: Math.max(10, Math.floor((100 - progress) * 2))
      }));
    }, 800);
  };

  const handleExportReport = () => {
    console.log('Exporting report...');
    // Simulate report generation
  };

  const handleStartNewCheck = () => {
    setCurrentStep('upload');
    setSelectedDocument(null);
    setResults(null);
    setProcessingData({
      progress: 0,
      stage: 'upload',
      estimatedTime: 180,
      creditsUsed: 5
    });
  };

  const handleSelectRecentCheck = (check) => {
    console.log('Selected recent check:', check.name);
    if (check.status === 'completed') {
      setResults(mockResults);
      setCurrentStep('results');
    }
  };

  const handleDeleteCheck = (checkId) => {
    console.log('Deleting check:', checkId);
  };

  const handleHighlightSelect = (match) => {
    console.log('Selected highlight:', match);
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <QuickActionSidebar />
      
      <main className={`${contentMargin} pt-16 transition-all duration-300 ease-in-out`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumbs />
          
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-text-primary mb-2">
                  Plagiarism Checker
                </h1>
                <p className="text-text-secondary">
                  Comprehensive originality verification with AI content detection
                </p>
              </div>
              
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2 px-4 py-2 bg-accent/10 rounded-lg">
                  <Icon name="Shield" size={16} className="text-accent" />
                  <span className="text-sm font-medium text-accent">Secure Processing</span>
                </div>
                <StatusNotification />
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* Primary Content Area */}
            <div className="xl:col-span-2 space-y-6">
              {currentStep === 'upload' && (
                <>
                  <UploadArea
                    onFileUpload={handleFileUpload}
                    onTextSubmit={handleTextSubmit}
                    isProcessing={false}
                  />
                  
                  {/* Features Overview */}
                  <div className="bg-surface rounded-lg border border-border p-6">
                    <h3 className="text-lg font-semibold text-text-primary mb-4">
                      Advanced Detection Features
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-primary/10 rounded-lg">
                          <Icon name="Globe" size={20} className="text-primary" />
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-text-primary">Web Scanning</h4>
                          <p className="text-xs text-text-secondary">
                            Comprehensive search across billions of web pages and online sources
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-secondary/10 rounded-lg">
                          <Icon name="GraduationCap" size={20} className="text-secondary" />
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-text-primary">Academic Databases</h4>
                          <p className="text-xs text-text-secondary">
                            Access to scholarly articles, journals, and academic publications
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-accent/10 rounded-lg">
                          <Icon name="Bot" size={20} className="text-accent" />
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-text-primary">AI Content Detection</h4>
                          <p className="text-xs text-text-secondary">
                            Advanced algorithms to identify AI-generated content
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start space-x-3">
                        <div className="p-2 bg-success/10 rounded-lg">
                          <Icon name="FileText" size={20} className="text-success" />
                        </div>
                        <div>
                          <h4 className="text-sm font-semibold text-text-primary">Document Repository</h4>
                          <p className="text-xs text-text-secondary">
                            Compare against previously submitted documents and papers
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {currentStep === 'processing' && (
                <ProcessingStatus
                  progress={processingData.progress}
                  stage={processingData.stage}
                  estimatedTime={processingData.estimatedTime}
                  creditsUsed={processingData.creditsUsed}
                />
              )}

              {currentStep === 'results' && (
                <>
                  <ResultsDashboard
                    results={results}
                    onExportReport={handleExportReport}
                    onStartNewCheck={handleStartNewCheck}
                  />
                  
                  <DocumentViewer
                    document={mockDocument}
                    matches={mockMatches}
                    onHighlightSelect={handleHighlightSelect}
                  />
                </>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              <RecentChecks
                onSelectCheck={handleSelectRecentCheck}
                onDeleteCheck={handleDeleteCheck}
              />
              
              {/* Credit Usage */}
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-text-primary">Credit Usage</h3>
                  <Icon name="Zap" size={16} className="text-accent" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-text-secondary">Available Credits</span>
                    <span className="text-sm font-medium text-accent">245</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-text-secondary">Used This Month</span>
                    <span className="text-sm font-medium text-text-primary">55</span>
                  </div>
                  
                  <div className="w-full bg-background rounded-full h-2">
                    <div className="bg-accent h-2 rounded-full" style={{ width: '18%' }} />
                  </div>
                  
                  <Button variant="outline" size="sm" className="w-full">
                    <Icon name="Plus" size={14} className="mr-2" />
                    Buy More Credits
                  </Button>
                </div>
              </div>

              {/* Security Notice */}
              <div className="bg-surface rounded-lg border border-border p-4">
                <div className="flex items-start space-x-3">
                  <Icon name="Lock" size={16} className="text-success mt-0.5" />
                  <div>
                    <h3 className="text-sm font-semibold text-text-primary mb-1">
                      Privacy & Security
                    </h3>
                    <ul className="text-xs text-text-secondary space-y-1">
                      <li>• Documents are encrypted during processing</li>
                      <li>• Automatic deletion after 24 hours</li>
                      <li>• No content stored on our servers</li>
                      <li>• GDPR compliant data handling</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default PlagiarismChecker;
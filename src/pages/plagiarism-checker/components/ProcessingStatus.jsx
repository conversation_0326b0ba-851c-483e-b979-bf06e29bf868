import React from 'react';
import Icon from '../../../components/AppIcon';

const ProcessingStatus = ({ progress, stage, estimatedTime, creditsUsed }) => {
  const stages = [
    { id: 'upload', label: 'Document Upload', icon: 'Upload' },
    { id: 'analysis', label: 'Content Analysis', icon: 'Search' },
    { id: 'scanning', label: 'Database Scanning', icon: 'Database' },
    { id: 'ai-detection', label: 'AI Content Detection', icon: 'Bot' },
    { id: 'report', label: 'Generating Report', icon: 'FileText' }
  ];

  const getCurrentStageIndex = () => {
    return stages.findIndex(s => s.id === stage);
  };

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}m ${secs}s` : `${secs}s`;
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      <div className="text-center mb-6">
        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <Icon name="Search" size={32} className="text-primary animate-pulse" />
        </div>
        <h3 className="text-lg font-semibold text-text-primary mb-2">
          Checking for Plagiarism
        </h3>
        <p className="text-text-secondary">
          Scanning your document against millions of sources...
        </p>
      </div>

      {/* Progress Bar */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-text-primary">Overall Progress</span>
          <span className="text-sm text-text-secondary">{progress}%</span>
        </div>
        <div className="w-full bg-background rounded-full h-2">
          <div
            className="bg-primary h-2 rounded-full transition-all duration-500"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Processing Stages */}
      <div className="space-y-3 mb-6">
        {stages.map((stageItem, index) => {
          const currentIndex = getCurrentStageIndex();
          const isCompleted = index < currentIndex;
          const isCurrent = index === currentIndex;
          const isPending = index > currentIndex;

          return (
            <div
              key={stageItem.id}
              className={`flex items-center space-x-3 p-3 rounded-lg transition-micro ${
                isCurrent ? 'bg-primary/10 border border-primary/20' : 'bg-background'
              }`}
            >
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center ${
                  isCompleted
                    ? 'bg-success text-success-foreground'
                    : isCurrent
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-border text-text-secondary'
                }`}
              >
                {isCompleted ? (
                  <Icon name="Check" size={16} />
                ) : isCurrent ? (
                  <Icon name={stageItem.icon} size={16} className="animate-pulse" />
                ) : (
                  <Icon name={stageItem.icon} size={16} />
                )}
              </div>
              <div className="flex-1">
                <p
                  className={`text-sm font-medium ${
                    isCurrent ? 'text-primary' : isCompleted ? 'text-success' : 'text-text-secondary'
                  }`}
                >
                  {stageItem.label}
                </p>
                {isCurrent && (
                  <p className="text-xs text-text-secondary">Processing...</p>
                )}
                {isCompleted && (
                  <p className="text-xs text-success">Completed</p>
                )}
              </div>
              {isCurrent && (
                <div className="w-4 h-4">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-primary border-t-transparent" />
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Status Information */}
      <div className="grid grid-cols-2 gap-4 p-4 bg-background rounded-lg">
        <div className="text-center">
          <Icon name="Clock" size={16} className="mx-auto text-text-secondary mb-1" />
          <p className="text-xs text-text-secondary">Estimated Time</p>
          <p className="text-sm font-medium text-text-primary">
            {formatTime(estimatedTime)}
          </p>
        </div>
        <div className="text-center">
          <Icon name="Zap" size={16} className="mx-auto text-accent mb-1" />
          <p className="text-xs text-text-secondary">Credits Used</p>
          <p className="text-sm font-medium text-accent">{creditsUsed}</p>
        </div>
      </div>

      {/* Security Notice */}
      <div className="mt-4 p-3 bg-success/10 border border-success/20 rounded-lg">
        <div className="flex items-start space-x-2">
          <Icon name="Shield" size={16} className="text-success mt-0.5" />
          <div>
            <p className="text-sm font-medium text-success">Secure Processing</p>
            <p className="text-xs text-text-secondary">
              Your document is encrypted and will be automatically deleted after processing.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProcessingStatus;
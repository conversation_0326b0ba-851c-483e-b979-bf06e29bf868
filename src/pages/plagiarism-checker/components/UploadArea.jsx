import React, { useState, useRef } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';

const UploadArea = ({ onFileUpload, onTextSubmit, isProcessing }) => {
  const [activeTab, setActiveTab] = useState('upload');
  const [textContent, setTextContent] = useState('');
  const [isDragOver, setIsDragOver] = useState(false);
  const fileInputRef = useRef(null);

  const supportedFormats = [
    { name: 'Word Documents', extensions: '.doc, .docx', icon: 'FileText' },
    { name: 'PDF Files', extensions: '.pdf', icon: 'File' },
    { name: 'Text Files', extensions: '.txt, .rtf', icon: 'FileText' },
    { name: 'Google Docs', extensions: 'Import via URL', icon: 'Link' }
  ];

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFileUpload(files[0]);
    }
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      onFileUpload(file);
    }
  };

  const handleTextSubmit = () => {
    if (textContent.trim()) {
      onTextSubmit(textContent);
    }
  };

  return (
    <div className="bg-surface rounded-lg border border-border p-6">
      {/* Tab Navigation */}
      <div className="flex space-x-1 mb-6 bg-background rounded-lg p-1">
        <button
          onClick={() => setActiveTab('upload')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-micro ${
            activeTab === 'upload' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
          }`}
        >
          <Icon name="Upload" size={16} className="inline mr-2" />
          Upload File
        </button>
        <button
          onClick={() => setActiveTab('text')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-micro ${
            activeTab === 'text' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
          }`}
        >
          <Icon name="Type" size={16} className="inline mr-2" />
          Paste Text
        </button>
        <button
          onClick={() => setActiveTab('url')}
          className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-micro ${
            activeTab === 'url' ?'bg-surface text-text-primary shadow-elevation-1' :'text-text-secondary hover:text-text-primary'
          }`}
        >
          <Icon name="Link" size={16} className="inline mr-2" />
          Import URL
        </button>
      </div>

      {/* Upload Tab */}
      {activeTab === 'upload' && (
        <div>
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-micro ${
              isDragOver
                ? 'border-primary bg-primary/5' :'border-border hover:border-primary/50'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="mb-4">
              <Icon name="Upload" size={48} className="mx-auto text-text-secondary mb-4" />
              <h3 className="text-lg font-semibold text-text-primary mb-2">
                Drop your document here
              </h3>
              <p className="text-text-secondary mb-4">
                or click to browse files from your computer
              </p>
            </div>

            <Button
              variant="primary"
              onClick={() => fileInputRef.current?.click()}
              disabled={isProcessing}
              className="mb-4"
            >
              <Icon name="FolderOpen" size={16} className="mr-2" />
              Choose File
            </Button>

            <input
              ref={fileInputRef}
              type="file"
              accept=".doc,.docx,.pdf,.txt,.rtf"
              onChange={handleFileSelect}
              className="hidden"
            />

            <p className="text-xs text-text-secondary">
              Maximum file size: 10MB
            </p>
          </div>

          {/* Supported Formats */}
          <div className="mt-6">
            <h4 className="text-sm font-medium text-text-primary mb-3">Supported Formats</h4>
            <div className="grid grid-cols-2 gap-3">
              {supportedFormats.map((format, index) => (
                <div key={index} className="flex items-center space-x-3 p-3 bg-background rounded-lg">
                  <Icon name={format.icon} size={16} className="text-text-secondary" />
                  <div>
                    <p className="text-sm font-medium text-text-primary">{format.name}</p>
                    <p className="text-xs text-text-secondary">{format.extensions}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Text Tab */}
      {activeTab === 'text' && (
        <div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Paste your text content
            </label>
            <textarea
              value={textContent}
              onChange={(e) => setTextContent(e.target.value)}
              placeholder="Paste your document content here for plagiarism checking..."
              className="w-full h-64 p-4 border border-border rounded-lg resize-none focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={isProcessing}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <p className="text-sm text-text-secondary">
              {textContent.length} characters • Minimum 100 characters required
            </p>
            <Button
              variant="primary"
              onClick={handleTextSubmit}
              disabled={isProcessing || textContent.length < 100}
            >
              <Icon name="Search" size={16} className="mr-2" />
              Check Plagiarism
            </Button>
          </div>
        </div>
      )}

      {/* URL Tab */}
      {activeTab === 'url' && (
        <div>
          <div className="mb-4">
            <label className="block text-sm font-medium text-text-primary mb-2">
              Import from URL
            </label>
            <div className="flex space-x-3">
              <Input
                type="url"
                placeholder="https://docs.google.com/document/..."
                className="flex-1"
                disabled={isProcessing}
              />
              <Button
                variant="primary"
                disabled={isProcessing}
              >
                <Icon name="Download" size={16} className="mr-2" />
                Import
              </Button>
            </div>
          </div>

          <div className="bg-background rounded-lg p-4">
            <h4 className="text-sm font-medium text-text-primary mb-2">Supported URLs</h4>
            <ul className="text-sm text-text-secondary space-y-1">
              <li>• Google Docs (public or shared links)</li>
              <li>• Microsoft OneDrive documents</li>
              <li>• Dropbox shared files</li>
              <li>• Public web pages with text content</li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
};

export default UploadArea;
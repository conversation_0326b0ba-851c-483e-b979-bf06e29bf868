import React from 'react';
import Icon from '../../../components/AppIcon';

const CategoryTabs = ({ activeCategory, onCategoryChange, categories }) => {
  return (
    <div className="border-b border-border">
      <div className="flex space-x-8 overflow-x-auto scrollbar-hide">
        {categories.map((category) => (
          <button
            key={category.key}
            onClick={() => onCategoryChange(category.key)}
            className={`flex items-center space-x-2 py-4 px-2 border-b-2 transition-colors whitespace-nowrap ${
              activeCategory === category.key
                ? 'border-primary text-primary' :'border-transparent text-text-secondary hover:text-text-primary'
            }`}
          >
            <Icon name={category.icon} size={18} />
            <span className="font-medium">{category.label}</span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              activeCategory === category.key
                ? 'bg-primary/10 text-primary' :'bg-background text-text-secondary'
            }`}>
              {category.count}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default CategoryTabs;
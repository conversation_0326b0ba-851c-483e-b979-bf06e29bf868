import React, { useState, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';

const FeaturedCarousel = ({ featuredTemplates, onSelectTemplate }) => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  useEffect(() => {
    if (!isAutoPlaying || featuredTemplates.length <= 1) return;

    const interval = setInterval(() => {
      setCurrentSlide(prev => (prev + 1) % featuredTemplates.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, featuredTemplates.length]);

  const goToSlide = (index) => {
    setCurrentSlide(index);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const nextSlide = () => {
    setCurrentSlide(prev => (prev + 1) % featuredTemplates.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const prevSlide = () => {
    setCurrentSlide(prev => (prev - 1 + featuredTemplates.length) % featuredTemplates.length);
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  if (!featuredTemplates || featuredTemplates.length === 0) {
    return null;
  }

  const currentTemplate = featuredTemplates[currentSlide];

  return (
    <div className="relative bg-gradient-to-r from-primary/10 to-secondary/10 rounded-xl overflow-hidden mb-8">
      <div className="relative h-64 lg:h-80">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src={currentTemplate.bannerImage}
            alt={currentTemplate.title}
            className="w-full h-full object-cover opacity-20"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-secondary/60"></div>
        </div>

        {/* Content */}
        <div className="relative z-10 h-full flex items-center">
          <div className="container mx-auto px-6 lg:px-8">
            <div className="max-w-2xl">
              <div className="flex items-center space-x-2 mb-4">
                <span className="bg-accent text-accent-foreground text-sm px-3 py-1 rounded-full font-medium">
                  Featured
                </span>
                <span className="text-primary-foreground/80 text-sm">
                  {currentTemplate.category}
                </span>
              </div>

              <h2 className="text-3xl lg:text-4xl font-bold text-primary-foreground mb-4">
                {currentTemplate.title}
              </h2>

              <p className="text-primary-foreground/90 text-lg mb-6 line-clamp-2">
                {currentTemplate.description}
              </p>

              <div className="flex items-center space-x-6 mb-6">
                <div className="flex items-center space-x-2 text-primary-foreground/80">
                  <Icon name="Users" size={16} />
                  <span className="text-sm">{currentTemplate.usageCount.toLocaleString()} uses</span>
                </div>
                <div className="flex items-center space-x-2 text-primary-foreground/80">
                  <Icon name="Star" size={16} />
                  <span className="text-sm">{currentTemplate.rating} rating</span>
                </div>
                <div className="flex items-center space-x-2 text-primary-foreground/80">
                  <Icon name="Clock" size={16} />
                  <span className="text-sm">{currentTemplate.estimatedTime}</span>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <Button
                  variant="secondary"
                  onClick={() => onSelectTemplate(currentTemplate)}
                  className="bg-white text-primary hover:bg-white/90"
                >
                  <Icon name="Plus" size={16} />
                  Use This Template
                </Button>
                <Button
                  variant="ghost"
                  className="text-primary-foreground border-primary-foreground/30 hover:bg-primary-foreground/10"
                >
                  <Icon name="Eye" size={16} />
                  Preview
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Arrows */}
        {featuredTemplates.length > 1 && (
          <>
            <button
              onClick={prevSlide}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-surface/20 hover:bg-surface/30 rounded-full flex items-center justify-center transition-colors"
            >
              <Icon name="ChevronLeft" size={20} color="white" />
            </button>
            <button
              onClick={nextSlide}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-surface/20 hover:bg-surface/30 rounded-full flex items-center justify-center transition-colors"
            >
              <Icon name="ChevronRight" size={20} color="white" />
            </button>
          </>
        )}
      </div>

      {/* Slide Indicators */}
      {featuredTemplates.length > 1 && (
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {featuredTemplates.map((_, index) => (
            <button
              key={index}
              onClick={() => goToSlide(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white/50'
              }`}
            />
          ))}
        </div>
      )}

      {/* Auto-play indicator */}
      <div className="absolute top-4 right-4">
        <button
          onClick={() => setIsAutoPlaying(!isAutoPlaying)}
          className="w-8 h-8 bg-surface/20 hover:bg-surface/30 rounded-full flex items-center justify-center transition-colors"
        >
          <Icon 
            name={isAutoPlaying ? "Pause" : "Play"} 
            size={14} 
            color="white" 
          />
        </button>
      </div>
    </div>
  );
};

export default FeaturedCarousel;
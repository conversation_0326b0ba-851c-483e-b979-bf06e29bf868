import React from "react";
import { BrowserRouter, Routes as RouterRout<PERSON>, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";
// Add your imports here
import Dashboard from "pages/dashboard";
import Projects from "pages/projects";
import TemplateLibrary from "pages/template-library";
import Plagiar<PERSON><PERSON><PERSON>cker from "pages/plagiarism-checker";
import CollaborationWorkspace from "pages/collaboration-workspace";
import AccountSettings from "pages/account-settings";
import DocumentCreator from "pages/document-creator";
import NotFound from "pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
      <ScrollToTop />
      <RouterRoutes>
        {/* Define your routes here */}
        <Route path="/" element={<Dashboard />} />
        <Route path="/dashboard" element={<Dashboard />} />
        <Route path="/projects" element={<Projects />} />
        <Route path="/template-library" element={<TemplateLibrary />} />
        <Route path="/plagiarism-checker" element={<PlagiarismChecker />} />
        <Route path="/collaboration-workspace" element={<CollaborationWorkspace />} />
        <Route path="/account-settings" element={<AccountSettings />} />
        <Route path="/document-creator" element={<DocumentCreator />} />
        <Route path="*" element={<NotFound />} />
      </RouterRoutes>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;
import React from 'react';
import * as LucideIcons from 'lucide-react';
import { HelpCircle } from 'lucide-react';

function Icon({
    name,
    size = 24,
    color = "currentColor",
    className = "",
    strokeWidth = 2,
    variant = "default",
    ...props
}) {
    const IconComponent = LucideIcons[name];

    // Predefined color variants that work with our design system
    const colorVariants = {
        default: "currentColor",
        primary: "text-primary",
        secondary: "text-secondary",
        accent: "text-accent",
        success: "text-success",
        warning: "text-warning",
        error: "text-error",
        muted: "text-text-muted",
        white: "text-white",
    };

    // Size presets for consistency
    const sizePresets = {
        xs: 12,
        sm: 16,
        md: 20,
        lg: 24,
        xl: 28,
        '2xl': 32,
    };

    // Determine final size
    const finalSize = typeof size === 'string' ? sizePresets[size] || 24 : size;

    // Determine final color
    const finalColor = colorVariants[variant] ? undefined : color;
    const colorClass = colorVariants[variant] || '';

    if (!IconComponent) {
        return (
            <HelpCircle
                size={finalSize}
                color={finalColor || "gray"}
                strokeWidth={strokeWidth}
                className={`${colorClass} ${className}`}
                {...props}
            />
        );
    }

    return (
        <IconComponent
            size={finalSize}
            color={finalColor}
            strokeWidth={strokeWidth}
            className={`${colorClass} ${className}`}
            {...props}
        />
    );
}

export default Icon;
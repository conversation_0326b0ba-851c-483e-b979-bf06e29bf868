import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSidebar } from '../../contexts/SidebarContext';
import Icon from '../AppIcon';

const QuickActionSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { isCollapsed, toggleSidebar, sidebarWidth } = useSidebar();

  // Main navigation items matching reference design
  const navigationItems = [
    {
      label: 'Home',
      icon: 'Home',
      path: '/dashboard',
      isActive: location.pathname === '/dashboard' || location.pathname === '/'
    },
    {
      label: 'Projects',
      icon: 'FolderOpen',
      path: '/projects',
      isActive: location.pathname === '/projects'
    },
    {
      label: 'Docs',
      icon: 'FileText',
      path: '/document-creator',
      isActive: location.pathname === '/document-creator'
    },
    {
      label: 'Media',
      icon: 'Image',
      path: '/template-library',
      isActive: location.pathname === '/template-library'
    }
  ];

  const handleNavigation = (path) => {
    navigate(path);
  };



  return (
    <aside className={`fixed left-0 top-0 h-screen ${sidebarWidth} bg-surface border-r border-border z-1000 shadow-sm hidden lg:block sidebar-transition`}>
      <div className="flex flex-col h-full">
        {/* Logo Section */}
        <div className="p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-primary to-hero-accent rounded-md flex items-center justify-center">
              <Icon name="FileText" size={18} color="white" />
            </div>
            {!isCollapsed && (
              <span className="text-lg font-semibold text-text-primary sidebar-content-transition">
                DocForge AI
              </span>
            )}
          </div>
        </div>

        {/* Navigation Items */}
        <nav className="flex-1 p-4">
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <button
                key={item.path}
                onClick={() => handleNavigation(item.path)}
                className={`w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'space-x-3 px-4'} py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
                  item.isActive
                    ? 'bg-primary text-white shadow-sm'
                    : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                }`}
                title={isCollapsed ? item.label : undefined}
              >
                <Icon name={item.icon} size={18} />
                {!isCollapsed && (
                  <span className="sidebar-content-transition">{item.label}</span>
                )}
              </button>
            ))}
          </div>
        </nav>

        {/* Upgrade Section */}
        <div className="p-4 border-t border-border">
          {isCollapsed ? (
            <button
              className="w-full p-3 bg-surface-secondary rounded-lg hover:bg-surface-hover transition-colors duration-200 flex items-center justify-center"
              title="Upgrades"
            >
              <Icon name="Zap" size={16} className="text-warning" />
            </button>
          ) : (
            <div className="bg-surface-secondary rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Icon name="Zap" size={16} className="text-warning" />
                <span className="text-sm font-medium text-text-primary">Upgrades</span>
              </div>
              <p className="text-xs text-text-secondary mb-3">
                Get access to more features and unlimited projects
              </p>
              <button className="w-full bg-primary text-white text-xs py-2 px-3 rounded-lg hover:bg-primary-dark transition-colors duration-200">
                Upgrade Plan
              </button>
            </div>
          )}
        </div>

        {/* Sidebar Toggle Button - Bottom Position */}
        <div className="p-4 border-t border-border">
          <button
            onClick={toggleSidebar}
            className={`w-full flex items-center ${isCollapsed ? 'justify-center px-2' : 'justify-center px-4'} py-3 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-all duration-200 group`}
            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}
          >
            <Icon
              name={isCollapsed ? "ChevronRight" : "ChevronLeft"}
              size={16}
              className="sidebar-toggle-icon"
            />
            {!isCollapsed && (
              <span className="ml-2 text-xs font-medium sidebar-content-transition">
                Collapse
              </span>
            )}
          </button>
        </div>
      </div>
    </aside>
  );
};

export default QuickActionSidebar;
import React, { useState, useEffect } from 'react';
import Icon from '../AppIcon';
import Button from './Button';

const StatusNotification = () => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'success',
      title: 'Document Generated',
      message: 'Your research paper has been successfully created.',
      timestamp: new Date(Date.now() - 5 * 60 * 1000),
      isRead: false,
      persistent: false
    },
    {
      id: 2,
      type: 'info',
      title: 'Collaboration Invite',
      message: '<PERSON> invited you to collaborate on "Market Analysis Report".',
      timestamp: new Date(Date.now() - 15 * 60 * 1000),
      isRead: false,
      persistent: true
    },
    {
      id: 3,
      type: 'warning',
      title: 'Credit Balance Low',
      message: 'You have 25 credits remaining. Consider upgrading your plan.',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      isRead: true,
      persistent: true
    }
  ]);

  const [isOpen, setIsOpen] = useState(false);
  const [activeToast, setActiveToast] = useState(null);

  const unreadCount = notifications.filter(n => !n.isRead).length;

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'success':
        return { name: 'CheckCircle', color: 'var(--color-success)' };
      case 'warning':
        return { name: 'AlertTriangle', color: 'var(--color-warning)' };
      case 'error':
        return { name: 'AlertCircle', color: 'var(--color-error)' };
      case 'info':
      default:
        return { name: 'Info', color: 'var(--color-secondary)' };
    }
  };

  const getNotificationBgColor = (type) => {
    switch (type) {
      case 'success':
        return 'bg-success/10 border-success/20';
      case 'warning':
        return 'bg-warning/10 border-warning/20';
      case 'error':
        return 'bg-error/10 border-error/20';
      case 'info':
      default:
        return 'bg-secondary/10 border-secondary/20';
    }
  };

  const formatTimestamp = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);

    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const handleNotificationClick = (notification) => {
    if (!notification.isRead) {
      setNotifications(prev =>
        prev.map(n =>
          n.id === notification.id ? { ...n, isRead: true } : n
        )
      );
    }

    // Handle specific notification actions
    switch (notification.type) {
      case 'info':
        if (notification.title.includes('Collaboration')) {
          console.log('Opening collaboration workspace...');
        }
        break;
      case 'warning':
        if (notification.title.includes('Credit')) {
          console.log('Opening billing settings...');
        }
        break;
      default:
        console.log('Notification clicked:', notification.title);
    }
  };

  const handleDismissNotification = (notificationId, event) => {
    event.stopPropagation();
    setNotifications(prev => prev.filter(n => n.id !== notificationId));
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev =>
      prev.map(n => ({ ...n, isRead: true }))
    );
  };

  const handleClearAll = () => {
    setNotifications(prev => prev.filter(n => n.persistent));
  };

  // Show toast notifications for new notifications
  useEffect(() => {
    const latestNotification = notifications.find(n => !n.isRead && !n.persistent);
    if (latestNotification && !activeToast) {
      setActiveToast(latestNotification);
      
      const timer = setTimeout(() => {
        setActiveToast(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [notifications, activeToast]);

  return (
    <>
      {/* Notification Bell Button */}
      <div className="relative">
        <Button
          variant="ghost"
          onClick={() => setIsOpen(!isOpen)}
          className="p-2 relative"
        >
          <Icon name="Bell" size={18} />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 w-5 h-5 bg-error text-error-foreground text-xs rounded-full flex items-center justify-center font-medium">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </Button>

        {/* Notification Panel */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-96 bg-surface rounded-lg shadow-elevation-3 border border-border z-1100 max-h-96 overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b border-border">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-text-primary">Notifications</h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <Button
                      variant="ghost"
                      onClick={handleMarkAllAsRead}
                      className="text-xs px-2 py-1"
                    >
                      Mark all read
                    </Button>
                  )}
                  <Button
                    variant="ghost"
                    onClick={handleClearAll}
                    className="text-xs px-2 py-1"
                  >
                    Clear all
                  </Button>
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-8 text-center">
                  <Icon name="Bell" size={32} className="mx-auto text-text-secondary mb-2" />
                  <p className="text-sm text-text-secondary">No notifications</p>
                </div>
              ) : (
                <div className="divide-y divide-border">
                  {notifications.map((notification) => {
                    const iconConfig = getNotificationIcon(notification.type);
                    return (
                      <div
                        key={notification.id}
                        onClick={() => handleNotificationClick(notification)}
                        className={`p-4 hover:bg-background cursor-pointer transition-micro ${
                          !notification.isRead ? 'bg-secondary/5' : ''
                        }`}
                      >
                        <div className="flex items-start space-x-3">
                          <div className={`p-1.5 rounded-lg ${getNotificationBgColor(notification.type)}`}>
                            <Icon name={iconConfig.name} size={16} color={iconConfig.color} />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between">
                              <p className={`text-sm ${!notification.isRead ? 'font-medium' : ''} text-text-primary`}>
                                {notification.title}
                              </p>
                              <div className="flex items-center space-x-1 ml-2">
                                {!notification.isRead && (
                                  <div className="w-2 h-2 bg-secondary rounded-full"></div>
                                )}
                                <Button
                                  variant="ghost"
                                  onClick={(e) => handleDismissNotification(notification.id, e)}
                                  className="p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                                >
                                  <Icon name="X" size={12} />
                                </Button>
                              </div>
                            </div>
                            <p className="text-xs text-text-secondary mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <p className="text-xs text-text-secondary mt-2">
                              {formatTimestamp(notification.timestamp)}
                            </p>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Toast Notification */}
      {activeToast && (
        <div className="fixed top-20 right-4 z-1200 animate-slide-up">
          <div className={`p-4 rounded-lg shadow-elevation-3 border max-w-sm ${getNotificationBgColor(activeToast.type)} bg-surface`}>
            <div className="flex items-start space-x-3">
              <div className={`p-1.5 rounded-lg ${getNotificationBgColor(activeToast.type)}`}>
                <Icon name={getNotificationIcon(activeToast.type).name} size={16} color={getNotificationIcon(activeToast.type).color} />
              </div>
              <div className="flex-1">
                <p className="text-sm font-medium text-text-primary">{activeToast.title}</p>
                <p className="text-xs text-text-secondary mt-1">{activeToast.message}</p>
              </div>
              <Button
                variant="ghost"
                onClick={() => setActiveToast(null)}
                className="p-1"
              >
                <Icon name="X" size={14} />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 z-1050"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default StatusNotification;
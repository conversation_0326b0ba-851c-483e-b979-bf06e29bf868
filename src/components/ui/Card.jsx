import React from 'react';

const Card = React.forwardRef(({
  children,
  variant = 'default',
  size = 'md',
  hover = true,
  clickable = false,
  className = '',
  onClick,
  ...props
}, ref) => {
  // Base classes
  const baseClasses = 'bg-white border border-border transition-all duration-300';

  // Variant classes
  const variantClasses = {
    default: 'shadow-card',
    elevated: 'shadow-elevated',
    soft: 'shadow-soft',
    hero: 'shadow-hero',
    flat: 'shadow-none border-border-strong',
  };

  // Size classes
  const sizeClasses = {
    sm: 'p-4 rounded-lg',
    md: 'p-6 rounded-xl',
    lg: 'p-8 rounded-xl',
    xl: 'p-10 rounded-2xl',
  };

  // Hover classes
  const hoverClasses = hover ? 'hover:shadow-elevated hover:-translate-y-1' : '';

  // Clickable classes
  const clickableClasses = clickable ? 'cursor-pointer active:translate-y-0 active:shadow-card' : '';

  return (
    <div
      ref={ref}
      onClick={onClick}
      className={`
        ${baseClasses}
        ${variantClasses[variant]}
        ${sizeClasses[size]}
        ${hoverClasses}
        ${clickableClasses}
        ${className}
      `}
      {...props}
    >
      {children}
    </div>
  );
});

Card.displayName = 'Card';

// Card Header Component
const CardHeader = ({ children, className = '', ...props }) => (
  <div className={`mb-4 ${className}`} {...props}>
    {children}
  </div>
);

// Card Title Component
const CardTitle = ({ children, className = '', ...props }) => (
  <h3 className={`text-lg font-semibold text-text-primary ${className}`} {...props}>
    {children}
  </h3>
);

// Card Description Component
const CardDescription = ({ children, className = '', ...props }) => (
  <p className={`text-sm text-text-secondary ${className}`} {...props}>
    {children}
  </p>
);

// Card Content Component
const CardContent = ({ children, className = '', ...props }) => (
  <div className={`${className}`} {...props}>
    {children}
  </div>
);

// Card Footer Component
const CardFooter = ({ children, className = '', ...props }) => (
  <div className={`mt-4 pt-4 border-t border-border ${className}`} {...props}>
    {children}
  </div>
);

// Export all components
export { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };
export default Card;
